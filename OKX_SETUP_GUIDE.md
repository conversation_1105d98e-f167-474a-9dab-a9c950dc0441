# 🚀 OKX 量化交易机器人设置指南

## 📋 快速开始

### 1. 环境准备

#### 系统要求
- **操作系统**: Windows 10+, macOS 10.14+, Linux
- **Python**: 3.8 或更高版本
- **内存**: 至少 4GB RAM
- **网络**: 稳定的互联网连接

#### 安装依赖
```bash
# 安装所有依赖包
pip install -r requirements.txt

# 或者手动安装主要依赖
pip install PyQt6 ccxt pandas numpy TA-Lib requests python-dotenv
```

### 2. OKX API 配置

#### 获取 API 密钥
1. 访问 [OKX 官网](https://www.okx.com)
2. 登录您的账户
3. 进入 **账户设置** → **API 管理**
4. 创建新的 API 密钥
5. 记录以下三个参数：
   - **API Key**: 公钥
   - **Secret Key**: 私钥  
   - **Passphrase**: 密码短语

#### API 权限设置
建议权限配置：
- ✅ **读取**: 查看账户信息
- ✅ **交易**: 下单和撤单
- ❌ **提现**: 不建议开启（安全考虑）

#### IP 白名单（推荐）
为了安全，建议设置 IP 白名单：
1. 获取您的公网 IP 地址
2. 在 OKX API 设置中添加 IP 白名单
3. 只允许指定 IP 访问

### 3. 配置方式

#### 方式一：环境变量文件（推荐）
1. 复制配置模板：
   ```bash
   cp .env.example .env
   ```

2. 编辑 `.env` 文件：
   ```env
   # OKX API 配置
   OKX_API_KEY=your_api_key_here
   OKX_SECRET_KEY=your_secret_key_here
   OKX_PASSPHRASE=your_passphrase_here
   
   # 可选配置
   NEWS_API_KEY=your_news_api_key
   DEEPSEEK_API_KEY=your_ai_api_key
   ```

#### 方式二：登录界面输入
如果没有配置 `.env` 文件，可以在登录界面手动输入：
- 启动程序后在登录界面输入三个参数
- 勾选"记住我"可以自动保存到 `.env` 文件

### 4. 启动程序

#### 使用启动脚本（推荐）
```bash
python3 start_okx_bot.py
```
启动脚本会自动检查环境和配置。

#### 直接启动
```bash
python3 login.py
```

### 5. 界面说明

#### 登录界面
- **API Key**: 输入您的 OKX API 密钥
- **Secret Key**: 输入您的 OKX 私钥
- **Passphrase**: 输入您的密码短语
- **记住我**: 勾选后会保存凭证到 `.env` 文件
- **清除保存**: 删除已保存的凭证

#### 主界面功能
- **交易对选择**: 支持主流加密货币交易对
- **技术指标**: RSI, MACD, 布林带等多种指标
- **AI 分析**: 集成 DeepSeek AI 智能分析
- **自动交易**: 基于策略的自动交易执行
- **风险管理**: 止盈止损设置

## ⚠️ 重要提醒

### 安全注意事项
1. **密钥保护**: 
   - 不要将 API 密钥分享给他人
   - 不要在公共场所输入密钥
   - 定期更换 API 密钥

2. **权限控制**:
   - 只开启必要的 API 权限
   - 不建议开启提现权限
   - 设置 IP 白名单限制访问

3. **资金安全**:
   - 建议先在模拟环境测试
   - 不要投入超过承受能力的资金
   - 设置合理的止损比例

### 风险提示
- **市场风险**: 加密货币市场波动较大，存在亏损风险
- **技术风险**: 量化交易策略可能失效，需要持续监控
- **网络风险**: 网络中断可能影响交易执行
- **API 风险**: API 限制或故障可能影响交易

## 🔧 故障排除

### 常见问题

#### 1. 登录失败
**问题**: API 验证失败
**解决方案**:
- 检查 API 密钥是否正确
- 确认 Passphrase 是否正确
- 检查 API 权限设置
- 验证 IP 白名单配置

#### 2. 网络连接问题
**问题**: 无法连接到 OKX
**解决方案**:
- 检查网络连接
- 确认防火墙设置
- 尝试使用 VPN（如果需要）

#### 3. 依赖包问题
**问题**: 缺少依赖包
**解决方案**:
```bash
pip install -r requirements.txt
```

#### 4. 交易对不存在
**问题**: 交易对格式错误
**解决方案**:
- OKX 现货格式: `BTC/USDT`
- OKX 永续合约格式: `BTC/USDT:USDT`

### 日志查看
程序运行时会在界面显示详细日志，包括：
- API 连接状态
- 交易执行记录
- 错误信息和警告
- 策略分析结果

## 📞 技术支持

### 获取帮助
1. **查看日志**: 程序界面的日志区域
2. **检查配置**: 确认 API 配置正确
3. **测试连接**: 使用测试脚本验证连接
4. **社区支持**: 查看项目文档和社区讨论

### 测试脚本
项目包含多个测试脚本：
- `test_okx_api.py`: 测试 OKX API 支持
- `test_okx_symbols.py`: 测试交易对格式
- `test_login_integration.py`: 测试登录集成
- `start_okx_bot.py`: 启动检查脚本

## 📈 使用建议

### 新手用户
1. **从小额开始**: 使用少量资金测试
2. **学习策略**: 了解技术指标和交易策略
3. **风险控制**: 设置合理的止损比例
4. **持续学习**: 关注市场动态和策略优化

### 高级用户
1. **策略优化**: 根据市场情况调整参数
2. **多币种交易**: 分散投资降低风险
3. **数据分析**: 利用历史数据优化策略
4. **自动化管理**: 设置自动化交易规则

---

**最后更新**: 2025-07-23  
**版本**: v2.0 (OKX 版)  
**状态**: 🚀 就绪使用
