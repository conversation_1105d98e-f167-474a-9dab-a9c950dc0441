#!/usr/bin/env python3
"""
OKX API 调试脚本
帮助诊断 API 认证问题
"""

import ccxt
import sys
import traceback

def test_okx_credentials():
    """详细测试 OKX API 凭证"""
    print("🔍 OKX API 凭证调试工具")
    print("=" * 50)
    
    # 获取用户输入
    print("请输入您的 OKX API 凭证:")
    api_key = input("API Key: ").strip()
    secret_key = input("Secret Key: ").strip()
    passphrase = input("Passphrase: ").strip()
    
    if not all([api_key, secret_key, passphrase]):
        print("❌ 请确保输入所有三个参数")
        return False
    
    print("\n📋 凭证信息检查:")
    print(f"API Key 长度: {len(api_key)}")
    print(f"Secret Key 长度: {len(secret_key)}")
    print(f"Passphrase 长度: {len(passphrase)}")
    
    # 检查基本格式
    if len(api_key) < 10:
        print("⚠️ API Key 长度可能过短")
    if len(secret_key) < 10:
        print("⚠️ Secret Key 长度可能过短")
    if len(passphrase) < 1:
        print("⚠️ Passphrase 不能为空")
    
    print("\n🔗 测试 OKX 连接...")
    
    try:
        # 创建 OKX 交易所实例
        exchange = ccxt.okx({
            'apiKey': api_key,
            'secret': secret_key,
            'password': passphrase,
            'enableRateLimit': True,
            'options': {
                'adjustForTimeDifference': True
            }
        })
        
        print("✅ OKX 实例创建成功")
        
        # 测试 1: 获取交易所状态
        print("\n📊 测试 1: 获取交易所状态...")
        try:
            status = exchange.fetch_status()
            print(f"✅ 交易所状态: {status.get('status', 'unknown')}")
        except Exception as e:
            print(f"⚠️ 获取状态失败: {e}")
        
        # 测试 2: 获取账户余额（需要认证）
        print("\n💰 测试 2: 获取账户余额...")
        try:
            balance = exchange.fetch_balance()
            print("✅ API 认证成功！")
            
            # 显示余额信息
            total_usdt = 0
            asset_count = 0
            for currency, amounts in balance.items():
                if currency not in ['info', 'free', 'used', 'total'] and amounts['total'] > 0:
                    asset_count += 1
                    if currency == 'USDT':
                        total_usdt = amounts['total']
                    print(f"  {currency}: {amounts['total']}")
            
            print(f"\n📈 账户概览:")
            print(f"  资产种类: {asset_count}")
            print(f"  USDT 余额: {total_usdt}")
            
            return True
            
        except ccxt.AuthenticationError as e:
            print(f"❌ 认证失败: {e}")
            print("\n🔧 可能的解决方案:")
            print("1. 检查 API Key 是否正确")
            print("2. 检查 Secret Key 是否正确")
            print("3. 检查 Passphrase 是否正确")
            print("4. 确认 API 权限包含 '读取' 权限")
            print("5. 检查 IP 白名单设置")
            print("6. 确认 API 状态为 '活跃'")
            return False
            
        except ccxt.NetworkError as e:
            print(f"❌ 网络错误: {e}")
            print("\n🔧 可能的解决方案:")
            print("1. 检查网络连接")
            print("2. 检查防火墙设置")
            print("3. 尝试使用 VPN")
            return False
            
        except Exception as e:
            print(f"❌ 其他错误: {e}")
            print(f"错误类型: {type(e).__name__}")
            print("\n详细错误信息:")
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ 创建交易所实例失败: {e}")
        return False

def test_market_data():
    """测试市场数据获取（不需要认证）"""
    print("\n📈 测试市场数据获取...")
    
    try:
        exchange = ccxt.okx()
        
        # 获取热门交易对的ticker
        symbols = ['BTC/USDT', 'ETH/USDT', 'BTC/USDT:USDT']
        
        for symbol in symbols:
            try:
                ticker = exchange.fetch_ticker(symbol)
                print(f"✅ {symbol}: ${ticker['last']:.2f}")
            except Exception as e:
                print(f"❌ {symbol}: {e}")
                
    except Exception as e:
        print(f"❌ 市场数据测试失败: {e}")

def main():
    """主函数"""
    print("🚀 OKX API 调试工具")
    print("此工具将帮助您诊断 API 认证问题")
    print("=" * 50)
    
    # 测试市场数据（不需要认证）
    test_market_data()
    
    print("\n" + "=" * 50)
    print("现在测试您的 API 凭证...")
    
    # 测试用户凭证
    success = test_okx_credentials()
    
    if success:
        print("\n🎉 恭喜！您的 OKX API 配置正确")
        print("现在可以正常使用量化交易机器人了")
    else:
        print("\n❌ API 配置有问题，请根据上述建议进行检查")
        print("\n💡 提示:")
        print("1. 确保在 OKX 官网正确创建了 API")
        print("2. 复制粘贴时避免多余的空格")
        print("3. 如果仍有问题，可以重新创建 API")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，退出程序")
    except Exception as e:
        print(f"\n❌ 程序异常: {e}")
        traceback.print_exc()
