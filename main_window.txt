import sys
from PyQt6.QtWidgets import (QMain<PERSON>indow, QWidget, QVBoxLayout, QHBoxLayout, 
                           QLabel, QPushButton, QComboBox, QDoubleSpinBox,
                           QTableWidget, QTableWidgetItem, QTabWidget,
                           QGridLayout, QFrame, QHeaderView, QApplication,
                           QMessageBox, QSplitter, QFormLayout, QLineEdit,
                           QGroupBox, QHBoxLayout, QTextEdit, QSpinBox, QDialog,
                           QScrollArea, QStatusBar, QRadioButton)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QObject, QRunnable, QThreadPool
from PyQt6.QtGui import QFont, QColor, QPixmap
import ccxt
from dotenv import load_dotenv
import os
import json
from datetime import datetime, timedelta
from chart_widget import ChartWidget
import requests
import threading
import re
import time
import pandas as pd
import talib
import gc
from requests.adapters import <PERSON>TT<PERSON>dapter
from urllib3.util.retry import Retry
from functools import lru_cache
from ccxt import OrderNotFound  # 添加这行导入

class CachedData:
    def __init__(self, data=None):
        self.data = data
        self.timestamp = datetime.now() if data else None
        
    def is_valid(self, ttl_seconds=60):
        if not self.timestamp:
            return False
        return (datetime.now() - self.timestamp).seconds < ttl_seconds

class MainWindow(QMainWindow):
    # 添加信号定义
    update_log_signal = pyqtSignal(str)
    show_message_signal = pyqtSignal(str, str, str)  # (title, message, type)
    network_status_signal = pyqtSignal(str, bool)    # (api_name, is_connected)
    
    def __init__(self):
        super().__init__()
        # 加载环境变量
        load_dotenv()
        self.api_key = os.getenv('OKX_API_KEY')
        self.secret_key = os.getenv('OKX_SECRET_KEY')
        self.passphrase = os.getenv('OKX_PASSPHRASE')
        
        # 初始化交易所API
        self.exchange = ccxt.okx({
            'apiKey': self.api_key,
            'secret': self.secret_key,
            'password': self.passphrase,
            'enableRateLimit': True
        })
        
        # 初始化News API和DeepSeek API密钥
        self.news_api_key = os.getenv('NEWS_API_KEY')
        self.deepseek_api_key = os.getenv('DEEPSEEK_API_KEY')
        
        # 加载技术指标设置
        self.load_indicator_settings()
        
        # 设置窗口属性
        self.setWindowTitle("OKX量化交易机器人")
        self.setMinimumSize(1200, 820)
        self.setStyleSheet("""
            QMainWindow {
                background-color: #0B0E11;
            }
            QLabel {
                color: #E6E8EA;
                font-size: 14px;
            }
            QWidget {
                background-color: #0B0E11;
            }
            QComboBox, QDoubleSpinBox {
                padding: 5px 10px;
                border: 1px solid #2a2e30;
                border-radius: 4px;
                background-color: #1b1e22;
                color: #E6E8EA;
                font-size: 14px;
                min-height: 20px;
            }
            QComboBox::drop-down {
                border: none;
                padding-right: 15px;
                width: 30px;
            }
            QComboBox::down-arrow {
                image: none;
                border: none;
            }
            QComboBox:hover, QDoubleSpinBox:hover {
                border: 1px solid #35383c;
            }
            QComboBox QAbstractItemView {
                border: 1px solid #2a2e30;
                selection-background-color: #2a2e30;
                background-color: #14151a;
                color: #E6E8EA;
            }
            QPushButton {
                padding: 8px 15px;
                background-color: #2EBD85;
                color: white;
                border: none;
                border-radius: 4px;
                font-size: 14px;
                font-weight: bold;
                min-height: 36px;
            }
            QPushButton:hover {
                background-color: #259C6C;
            }
            QPushButton[type="sell"] {
                background-color: #F6465D;
            }
            QPushButton[type="sell"]:hover {
                background-color: #E0364D;
            }
            QPushButton[type="delete"] {
                background-color: #F6465D;
            }
            QPushButton[type="delete"]:hover {
                background-color: #E0364D;
            }
            QPushButton[type="neutral"] {
                background-color: #2a2e30;
                color: #E6E8EA;
            }
            QPushButton[type="neutral"]:hover {
                background-color: #35383c;
            }
            QPushButton[type="primary"] {
                background-color: #F0B90B;
                color: #0B0E11;
            }
            QPushButton[type="primary"]:hover {
                background-color: #F8D33A;
            }
            QTableWidget {
                background-color: #0B0E11;
                color: #E6E8EA;
                gridline-color: #2a2e30;
                border: none;
                border-radius: 4px;
                outline: none;
            }
            QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #2a2e30;
            }
            QTableWidget::item:selected {
                background-color: #2a2e30;
                color: #F0B90B;
            }
            QHeaderView::section {
                background-color: #14151a;
                color: #848E9C;
                padding: 10px;
                border: none;
                border-bottom: 1px solid #2a2e30;
                font-weight: bold;
            }
            QTabWidget::pane {
                border: 1px solid #2a2e30;
                background-color: #0B0E11;
                border-radius: 4px;
            }
            QTabBar::tab {
                background-color: #14151a;
                color: #848E9C;
                padding: 12px 20px;
                border: none;
                min-width: 120px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: #0B0E11;
                color: #F0B90B;
                border-bottom: 2px solid #F0B90B;
            }
            QTabBar::tab:hover:!selected {
                color: #E6E8EA;
                background-color: #1b1e22;
            }
            QSplitter::handle {
                background-color: #2a2e30;
                height: 1px;
                width: 1px;
            }
            QSplitter::handle:horizontal {
                width: 1px;
            }
            QSplitter::handle:vertical {
                height: 1px;
            }
            QGroupBox {
                border: 1px solid #2a2e30;
                border-radius: 4px;
                margin-top: 1.5em;
                padding-top: 15px;
                color: #E6E8EA;
                font-weight: bold;
                font-size: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
                color: #F0B90B;
            }
            QScrollBar:vertical {
                background: #14151a;
                width: 8px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background: #2a2e30;
                min-height: 30px;
                border-radius: 4px;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }
            QScrollBar:horizontal {
                background: #14151a;
                height: 8px;
                margin: 0px;
            }
            QScrollBar::handle:horizontal {
                background: #2a2e30;
                min-width: 30px;
                border-radius: 4px;
            }
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                width: 0px;
            }
            QLineEdit {
                padding: 8px 12px;
                border: 1px solid #2a2e30;
                border-radius: 4px;
                background-color: #1b1e22;
                color: #E6E8EA;
                font-size: 14px;
                min-height: 20px;
            }
            QLineEdit:focus {
                border: 1px solid #35383c;
            }
            QSpinBox, QDoubleSpinBox {
                padding: 8px;
                border: 1px solid #2a2e30;
                border-radius: 4px;
                background-color: #1b1e22;
                color: #E6E8EA;
                font-size: 14px;
                min-height: 20px;
            }
            QSpinBox::up-button, QSpinBox::down-button,
            QDoubleSpinBox::up-button, QDoubleSpinBox::down-button {
                width: 22px;
                background-color: #2a2e30;
                border: none;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover,
            QDoubleSpinBox::up-button:hover, QDoubleSpinBox::down-button:hover {
                background-color: #35383c;
            }
            QTextEdit {
                background-color: #14151a;
                color: #E6E8EA;
                border: 1px solid #2a2e30;
                border-radius: 4px;
                padding: 12px;
                selection-background-color: #2a2e30;
            }
            QCheckBox {
                color: #E6E8EA;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                border: 1px solid #2a2e30;
                background-color: #1b1e22;
            }
            QCheckBox::indicator:checked {
                background-color: #F0B90B;
                border: 1px solid #F0B90B;
            }
            QCheckBox::indicator:unchecked:hover {
                border: 1px solid #F0B90B;
            }
            QRadioButton {
                color: #E6E8EA;
            }
            QRadioButton::indicator {
                width: 18px;
                height: 18px;
                border-radius: 9px;
                border: 1px solid #2a2e30;
                background-color: #1b1e22;
            }
            QRadioButton::indicator:checked {
                background-color: #F0B90B;
                border: 1px solid #F0B90B;
            }
            QRadioButton::indicator:unchecked:hover {
                border: 1px solid #F0B90B;
            }
        """)
        
        # 添加状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 网络状态指示器
        self.exchange_status = QLabel("交易所连接: 未知")
        self.exchange_status.setStyleSheet("color: #848E9C;")
        self.news_api_status = QLabel("News API: 未知")
        self.news_api_status.setStyleSheet("color: #848E9C;")
        self.ai_api_status = QLabel("AI API: 未知")
        self.ai_api_status.setStyleSheet("color: #848E9C;")
        
        # 添加手动检查网络按钮
        check_network_btn = QPushButton("检查网络")
        check_network_btn.setProperty("type", "neutral")
        check_network_btn.setFixedSize(90, 26)
        check_network_btn.setStyleSheet("""
            QPushButton {
                padding: 3px 8px;
                font-size: 12px;
                min-height: 0px;
            }
        """)
        check_network_btn.clicked.connect(self.check_network_status)
        
        self.status_bar.addPermanentWidget(self.exchange_status)
        self.status_bar.addPermanentWidget(self.news_api_status)
        self.status_bar.addPermanentWidget(self.ai_api_status)
        self.status_bar.addPermanentWidget(check_network_btn)
        
        self.init_ui()
        self.log_trading("程序初始化完成")  # 测试日志
        
        # 启动定时器更新数据
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_market_data)
        self.update_timer.start(2000)  # 每2秒更新一次
        
        # 添加线程锁
        self.trade_lock = threading.Lock()
        self.log_lock = threading.Lock()
        
        # 连接信号到日志更新方法
        self.update_log_signal.connect(self.log_trading_main_thread)
        self.show_message_signal.connect(self.show_message_box)
        
        # 加载配置文件
        self.load_config()
        
        # 添加缓存对象
        self._market_data_cache = {}
        
        # 添加性能优化相关变量
        self.last_update_time = time.time()
        self.update_interval = 2.0  # 默认更新间隔2秒
        self.is_updating = False  # 防止重复更新
        self.data_cache = {}  # 数据缓存
        self.thread_pool = QThreadPool.globalInstance()  # 使用线程池
        
        # 连接网络状态信号
        self.network_status_signal.connect(self.update_network_status)
        
        # 启动网络状态检查
        self.network_check_timer = QTimer()
        self.network_check_timer.timeout.connect(self.check_network_status)
        self.network_check_timer.start(60000)  # 每60秒检查一次网络状态
        
        # 初始网络状态检查
        QTimer.singleShot(2000, self.check_network_status)
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        layout.setSpacing(10)
        layout.setContentsMargins(10, 10, 10, 10)
        
        # 顶部面板 - 交易对选择
        top_panel = QWidget()
        top_panel.setStyleSheet("""
            QWidget {
                background-color: #14151a;
                border-radius: 6px;
            }
        """)
        top_layout = QHBoxLayout(top_panel)
        top_layout.setContentsMargins(15, 15, 15, 15)
        top_layout.setSpacing(15)
        
        self.instType_combo = QComboBox()
        self.instType_combo.addItems(['永续合约', '现货', '交割合约'])
        self.symbol_combo = QComboBox()
        self.symbol_combo.addItems([
            'BTC/USDT', 'ETH/USDT', 'BNB/USDT',  # 主流币
            'SOL/USDT', 'XRP/USDT', 'ADA/USDT',  # 其他热门币
            'DOGE/USDT', 'SHIB/USDT',            # 迷因币
            'DOT/USDT', 'AVAX/USDT',             # 公链
            'LTC/USDT', 'BCH/USDT',              # 老牌币
            'MATIC/USDT', 'LINK/USDT',           # 其他
            'ATOM/USDT', 'UNI/USDT',             # DeFi
            'TRX/USDT', 'ETC/USDT'               # 其他
        ])
        self.symbol_combo.currentTextChanged.connect(self.on_symbol_changed)
        
        label_style = "color: #848E9C; font-size: 13px; font-weight: normal;"
        
        symbol_label = QLabel("交易对:")
        symbol_label.setStyleSheet(label_style)
        
        top_layout.addWidget(symbol_label)
        top_layout.addWidget(self.symbol_combo)
        
        # 添加价格显示到顶部面板
        price_section = QWidget()
        price_section.setStyleSheet("background-color: transparent;")
        price_layout = QVBoxLayout(price_section)
        price_layout.setContentsMargins(0, 0, 0, 0)
        price_layout.setSpacing(5)
        
        # 实时价格显示
        self.price_display = QLabel("当前价格: -")
        self.price_display.setStyleSheet("font-size: 22px; font-weight: bold; color: #F0B90B;")
        price_layout.addWidget(self.price_display)
        
        # 实时波动显示
        self.volatility_display = QLabel("当前波动: -")
        self.volatility_display.setStyleSheet("font-size: 15px; color: #848E9C;")
        price_layout.addWidget(self.volatility_display)
        
        # 涨幅显示
        self.change_display = QLabel("24小时涨幅: -")
        self.change_display.setStyleSheet("font-size: 15px; color: #848E9C;")
        price_layout.addWidget(self.change_display)
        
        top_layout.addWidget(price_section)
        
        # 添加弹性空间
        top_layout.addStretch(1)
        
        # 将止盈止损设置移动到顶部区域
        tp_sl_group = QGroupBox("止盈止损设置")
        tp_sl_layout = QGridLayout()
        tp_sl_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                margin-top: 0.5em;
                padding-top: 10px;
                background-color: transparent;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 5px;
                padding: 0 5px;
                color: #F0B90B;
            }
        """)
        
        tp_sl_layout.setVerticalSpacing(8)
        tp_sl_layout.setContentsMargins(10, 15, 10, 10)

        # 止盈价调整
        self.tp_spinbox = QDoubleSpinBox()
        self.tp_spinbox.setRange(0.1, 1000)  # 设置合理范围
        self.tp_spinbox.setDecimals(2)  # 两位小数
        self.tp_spinbox.setValue(1.5)  # 默认值修改为1.5
        self.tp_spinbox.setSuffix("%")
        self.tp_spinbox.setStyleSheet("""
            QDoubleSpinBox {
                padding: 5px;
                border: 1px solid #2a2e30;
                border-radius: 4px;
                background-color: #1b1e22;
                color: #E6E8EA;
                font-size: 13px;
            }
            QDoubleSpinBox:focus {
                border: 1px solid #35383c;
            }
            QDoubleSpinBox::up-button, QDoubleSpinBox::down-button {
                width: 18px;
                background-color: #2a2e30;
                border: none;
            }
            QDoubleSpinBox::up-button:hover, QDoubleSpinBox::down-button:hover {
                background-color: #35383c;
            }
        """)
        tp_sl_layout.addWidget(QLabel("止盈:"), 0, 0)
        tp_sl_layout.addWidget(self.tp_spinbox, 0, 1)

        # 止损价调整
        self.sl_spinbox = QDoubleSpinBox()
        self.sl_spinbox.setRange(0.1, 1000)
        self.sl_spinbox.setDecimals(2)
        self.sl_spinbox.setValue(1.5)  # 默认值修改为1.5
        self.sl_spinbox.setSuffix("%")
        self.sl_spinbox.setStyleSheet("""
            QDoubleSpinBox {
                padding: 5px;
                border: 1px solid #2a2e30;
                border-radius: 4px;
                background-color: #1b1e22;
                color: #E6E8EA;
                font-size: 13px;
            }
            QDoubleSpinBox:focus {
                border: 1px solid #35383c;
            }
            QDoubleSpinBox::up-button, QDoubleSpinBox::down-button {
                width: 18px;
                background-color: #2a2e30;
                border: none;
            }
            QDoubleSpinBox::up-button:hover, QDoubleSpinBox::down-button:hover {
                background-color: #35383c;
            }
        """)
        tp_sl_layout.addWidget(QLabel("止损:"), 1, 0)
        tp_sl_layout.addWidget(self.sl_spinbox, 1, 1)

        # 应用按钮
        apply_tp_sl_button = QPushButton("应用")
        apply_tp_sl_button.setStyleSheet("""
            QPushButton {
                background-color: #2a2e30;
                color: #E6E8EA;
                border: none;
                border-radius: 4px;
                padding: 5px 10px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #35383c;
            }
        """)
        apply_tp_sl_button.clicked.connect(self.apply_tp_sl_settings)
        tp_sl_layout.addWidget(apply_tp_sl_button, 0, 2, 2, 1)  # 让按钮跨两行

        tp_sl_group.setLayout(tp_sl_layout)
        top_layout.addWidget(tp_sl_group)
        
        # 将触发分析设置移动到止盈止损设置后面
        trigger_group = QGroupBox("触发分析")
        trigger_layout = QGridLayout()
        trigger_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                margin-top: 0.5em;
                padding-top: 10px;
                background-color: transparent;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 5px;
                padding: 0 5px;
                color: #F0B90B;
            }
        """)
        
        trigger_layout.setVerticalSpacing(8)
        trigger_layout.setContentsMargins(10, 15, 10, 10)

        # 触发分析价格波动阈值
        self.trigger_threshold_spinbox = QDoubleSpinBox()
        self.trigger_threshold_spinbox.setRange(0.1, 10.0)  # 设置合理范围
        self.trigger_threshold_spinbox.setDecimals(2)  # 两位小数
        self.trigger_threshold_spinbox.setValue(0.25)  # 默认值
        self.trigger_threshold_spinbox.setSuffix("%")
        self.trigger_threshold_spinbox.setStyleSheet("""
            QDoubleSpinBox {
                padding: 5px;
                border: 1px solid #2a2e30;
                border-radius: 4px;
                background-color: #1b1e22;
                color: #E6E8EA;
                font-size: 13px;
            }
            QDoubleSpinBox:focus {
                border: 1px solid #35383c;
            }
            QDoubleSpinBox::up-button, QDoubleSpinBox::down-button {
                width: 18px;
                background-color: #2a2e30;
                border: none;
            }
            QDoubleSpinBox::up-button:hover, QDoubleSpinBox::down-button:hover {
                background-color: #35383c;
            }
        """)
        trigger_layout.addWidget(QLabel("价格波动阈值:"), 0, 0)
        trigger_layout.addWidget(self.trigger_threshold_spinbox, 0, 1)
        
        trigger_group.setLayout(trigger_layout)
        top_layout.addWidget(trigger_group)
        
        # 添加技术面指标设置按钮
        indicator_settings_button = QPushButton("技术指标设置")
        indicator_settings_button.setStyleSheet("""
            QPushButton {
                background-color: #F0B90B;
                color: #0B0E11;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-weight: bold;
                font-size: 14px;
                min-height: 36px;
                max-height: 38px;
            }
            QPushButton:hover {
                background-color: #F8D33A;
            }
        """)
        indicator_settings_button.clicked.connect(self.show_indicator_settings)
        top_layout.addWidget(indicator_settings_button)
        
        # 完成顶部面板
        layout.addWidget(top_panel)
        
        # 主面板 - 使用QSplitter
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # 左侧面板 - K线图
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)
        left_layout.setContentsMargins(10, 10, 10, 10)
        
        # K线图
        self.chart_widget = ChartWidget()
        left_layout.addWidget(self.chart_widget)
        
        # 连接图表的时间周期变化信号以刷新数据
        self.chart_widget.timeframe_changed.connect(self.refresh_chart_data)
        
        # 右侧面板 - 账户信息和AI交易
        right_panel = QTabWidget()
        right_panel.setStyleSheet("""
            QTabBar::tab {
                padding: 8px 16px;
            }
        """)
        
        # 账户信息标签页
        account_tab = QWidget()
        account_layout = QVBoxLayout(account_tab)
        account_layout.setContentsMargins(15, 15, 15, 15)
        account_layout.setSpacing(15)
        
        # 添加账户资产概览卡片（新增）
        account_summary_layout = QHBoxLayout()
        account_summary_layout.setSpacing(10)
        
        # USDT总资产卡片
        usdt_card = QWidget()
        usdt_card.setObjectName("summary_card")
        usdt_card.setStyleSheet("""
            #summary_card {
                background-color: #252930;
                border-radius: 8px;
                padding: 5px;
            }
        """)
        usdt_layout = QVBoxLayout(usdt_card)
        usdt_layout.setContentsMargins(15, 15, 15, 15)
        
        usdt_title = QLabel("USDT 总资产")
        usdt_title.setStyleSheet("color: #848E9C; font-size: 13px;")
        self.usdt_value = QLabel("0.00")
        self.usdt_value.setStyleSheet("color: #F0B90B; font-size: 22px; font-weight: bold;")
        
        usdt_layout.addWidget(usdt_title)
        usdt_layout.addWidget(self.usdt_value)
        
        # BTC总资产卡片
        btc_card = QWidget()
        btc_card.setObjectName("summary_card")
        btc_card.setStyleSheet("""
            #summary_card {
                background-color: #252930;
                border-radius: 8px;
                padding: 5px;
            }
        """)
        btc_layout = QVBoxLayout(btc_card)
        btc_layout.setContentsMargins(15, 15, 15, 15)
        
        btc_title = QLabel("BTC 总资产")
        btc_title.setStyleSheet("color: #848E9C; font-size: 13px;")
        self.btc_value = QLabel("0.00000000")
        self.btc_value.setStyleSheet("color: #F0B90B; font-size: 22px; font-weight: bold;")
        
        btc_layout.addWidget(btc_title)
        btc_layout.addWidget(self.btc_value)
        
        # 未实现盈亏卡片
        pnl_card = QWidget()
        pnl_card.setObjectName("summary_card")
        pnl_card.setStyleSheet("""
            #summary_card {
                background-color: #252930;
                border-radius: 8px;
                padding: 5px;
            }
        """)
        pnl_layout = QVBoxLayout(pnl_card)
        pnl_layout.setContentsMargins(15, 15, 15, 15)
        
        pnl_title = QLabel("未实现盈亏")
        pnl_title.setStyleSheet("color: #848E9C; font-size: 13px;")
        self.pnl_value = QLabel("0.00")
        self.pnl_value.setStyleSheet("color: #2EBD85; font-size: 22px; font-weight: bold;")
        
        pnl_layout.addWidget(pnl_title)
        pnl_layout.addWidget(self.pnl_value)
        
        # 将卡片添加到摘要布局
        account_summary_layout.addWidget(usdt_card, 1)
        account_summary_layout.addWidget(btc_card, 1)
        account_summary_layout.addWidget(pnl_card, 1)
        
        # 添加摘要布局到账户布局
        account_layout.addLayout(account_summary_layout)
        
        # 添加刷新按钮（整合进标题栏）
        asset_header = QWidget()
        asset_header_layout = QHBoxLayout(asset_header)
        asset_header_layout.setContentsMargins(0, 0, 0, 0)
        
        asset_title = QLabel("账户资产")
        asset_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #F0B90B;")
        
        refresh_account_btn = QPushButton("刷新")
        refresh_account_btn.setProperty("type", "neutral")
        refresh_account_btn.setFixedSize(80, 28)
        refresh_account_btn.setStyleSheet("""
            QPushButton {
                background-color: #1b1e22;
                border: 1px solid #2a2e30;
                padding: 3px 8px;
                font-size: 12px;
                min-height: 0px;
            }
            QPushButton:hover {
                background-color: #2a2e30;
            }
        """)
        refresh_account_btn.clicked.connect(self.update_market_data)
        
        asset_header_layout.addWidget(asset_title)
        asset_header_layout.addStretch()
        asset_header_layout.addWidget(refresh_account_btn)
        
        # 账户资产表格（优化现有表格）
        account_group = QGroupBox()
        account_group_layout = QVBoxLayout()
        account_group_layout.setContentsMargins(0, 0, 0, 0)
        account_group_layout.addWidget(asset_header)
        
        self.account_table = QTableWidget()
        self.account_table.setColumnCount(4)
        self.account_table.setHorizontalHeaderLabels(['资产', '可用', '已用', '总计'])
        self.account_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.account_table.setStyleSheet("""
            QTableWidget {
                background-color: #1A1A1A;
                border: 1px solid #2A2A2A;
                gridline-color: #2A2A2A;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #2A2A2A;
            }
            QHeaderView::section {
                background-color: #252930;
                color: #848E9C;
                padding: 5px;
                border: none;
                border-right: 1px solid #2A2A2A;
                border-bottom: 1px solid #2A2A2A;
            }
        """)
        account_group_layout.addWidget(self.account_table)
        account_group.setLayout(account_group_layout)
        account_layout.addWidget(account_group)
        
        # 创建分割器来分隔持仓和委托订单（改进现有分隔器）
        positions_splitter = QSplitter(Qt.Orientation.Vertical)
        positions_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: #2a2e30;
                height: 2px;
            }
        """)
        
        # 持仓信息面板（改进标题栏）
        positions_group = QGroupBox()
        positions_group_layout = QVBoxLayout()
        positions_group_layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加持仓标题栏
        positions_header = QWidget()
        positions_header_layout = QHBoxLayout(positions_header)
        positions_header_layout.setContentsMargins(0, 0, 0, 0)
        
        positions_title = QLabel("当前持仓")
        positions_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #F0B90B;")
        
        positions_header_layout.addWidget(positions_title)
        positions_header_layout.addStretch()
        
        positions_group_layout.addWidget(positions_header)
        
        # 持仓表格（优化样式）
        self.positions_table = QTableWidget()
        self.positions_table.setColumnCount(5)
        self.positions_table.setHorizontalHeaderLabels(['交易对', '持仓量', '开仓价格', '标记价格', '未实现盈亏'])
        self.positions_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.positions_table.setStyleSheet("""
            QTableWidget {
                background-color: #1A1A1A;
                border: 1px solid #2A2A2A;
                gridline-color: #2A2A2A;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #2A2A2A;
            }
            QHeaderView::section {
                background-color: #252930;
                color: #848E9C;
                padding: 5px;
                border: none;
                border-right: 1px solid #2A2A2A;
                border-bottom: 1px solid #2A2A2A;
            }
        """)
        positions_group_layout.addWidget(self.positions_table)
        positions_group.setLayout(positions_group_layout)
        
        # 委托订单面板（改进标题栏）
        orders_group = QGroupBox()
        orders_group_layout = QVBoxLayout()
        orders_group_layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加订单标题栏
        orders_header = QWidget()
        orders_header_layout = QHBoxLayout(orders_header)
        orders_header_layout.setContentsMargins(0, 0, 0, 0)
        
        orders_title = QLabel("委托订单")
        orders_title.setStyleSheet("font-size: 16px; font-weight: bold; color: #F0B90B;")
        
        orders_header_layout.addWidget(orders_title)
        orders_header_layout.addStretch()
        
        orders_group_layout.addWidget(orders_header)
        
        # 订单表格（优化样式）
        self.orders_table = QTableWidget()
        self.orders_table.setColumnCount(7)
        self.orders_table.setHorizontalHeaderLabels([
            '订单ID', '交易对', '方向', '类型', '价格', '数量', '状态'
        ])
        self.orders_table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.orders_table.setStyleSheet("""
            QTableWidget {
                background-color: #1A1A1A;
                border: 1px solid #2A2A2A;
                gridline-color: #2A2A2A;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #2A2A2A;
            }
            QHeaderView::section {
                background-color: #252930;
                color: #848E9C;
                padding: 5px;
                border: none;
                border-right: 1px solid #2A2A2A;
                border-bottom: 1px solid #2A2A2A;
            }
        """)
        orders_group_layout.addWidget(self.orders_table)
        
        # 按钮布局（优化样式）
        orders_button_layout = QHBoxLayout()
        orders_button_layout.setContentsMargins(0, 10, 0, 0)
        
        # 添加取消订单按钮
        cancel_button = QPushButton("取消选中订单")
        cancel_button.setProperty("type", "danger")
        cancel_button.setStyleSheet("""
            QPushButton[type="danger"] {
                background-color: #F23645;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-size: 13px;
            }
            QPushButton[type="danger"]:hover {
                background-color: #FF4D5B;
            }
        """)
        cancel_button.clicked.connect(self.cancel_selected_orders)
        orders_button_layout.addWidget(cancel_button)
        
        # 添加刷新订单按钮
        refresh_button = QPushButton("刷新订单数据")
        refresh_button.setProperty("type", "primary")
        refresh_button.setStyleSheet("""
            QPushButton[type="primary"] {
                background-color: #F0B90B;
                color: black;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                font-size: 13px;
            }
            QPushButton[type="primary"]:hover {
                background-color: #F8D33A;
            }
        """)
        refresh_button.clicked.connect(self.refresh_order_data)
        orders_button_layout.addWidget(refresh_button)
        
        orders_group_layout.addLayout(orders_button_layout)
        orders_group.setLayout(orders_group_layout)
        
        # 将面板添加到分割器
        positions_splitter.addWidget(positions_group)
        positions_splitter.addWidget(orders_group)
        account_layout.addWidget(positions_splitter)
        
        right_panel.addTab(account_tab, "账户资产")
        
        # AI交易标签页
        ai_trading_tab = QWidget()
        ai_trading_layout = QVBoxLayout(ai_trading_tab)
        ai_trading_layout.setContentsMargins(15, 15, 15, 15)
        ai_trading_layout.setSpacing(15)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)  # 允许内容自适应大小
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: #1A1A1A;  /* 深黑色背景 */
            }
            QScrollArea > QWidget > QWidget {
                background-color: #1A1A1A;  /* 设置滚动内容的背景色 */
            }
            QScrollBar:vertical {
                background: #2A2A2A;
                width: 10px;
                margin: 0px 0px 0px 0px;
            }
            QScrollBar::handle:vertical {
                background: #444444;
                min-height: 20px;
                border-radius: 5px;
            }
            QScrollBar::add-line:vertical,
            QScrollBar::sub-line:vertical {
                background: none;
            }
            QScrollBar::add-page:vertical,
            QScrollBar::sub-page:vertical {
                background: none;
            }
        """)
        
        # 创建滚动内容容器
        scroll_content = QWidget()
        scroll_content_layout = QVBoxLayout(scroll_content)
        scroll_content_layout.setContentsMargins(10, 10, 10, 10)
        scroll_content_layout.setSpacing(10)
        
        # 将原有内容添加到滚动内容容器中
        # AI交易控制面板
        control_group = QGroupBox("AI交易控制")
        control_layout = QGridLayout()
        control_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 15px;
                margin-top: 1.5em;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px;
                color: #F0B90B;
            }
        """)
        
        # 移除原来的价格显示，因为已经移到顶部
        # 添加间隔
        control_layout.setVerticalSpacing(15)
        control_layout.setContentsMargins(15, 20, 15, 20)
        
        # 交易对选择
        self.ai_trading_symbol_combo = QComboBox()
        self.ai_trading_symbol_combo.addItems([
            'BTC/USDT', 'ETH/USDT', 'BNB/USDT',  # 主流币
            'SOL/USDT', 'XRP/USDT', 'ADA/USDT',  # 其他热门币
            'DOGE/USDT', 'SHIB/USDT',            # 迷因币
            'DOT/USDT', 'AVAX/USDT',             # 公链
            'LTC/USDT', 'BCH/USDT',              # 老牌币
            'MATIC/USDT', 'LINK/USDT',           # 其他
            'ATOM/USDT', 'UNI/USDT',             # DeFi
            'TRX/USDT', 'ETC/USDT'               # 其他
        ])
        self.ai_trading_symbol_combo.currentTextChanged.connect(self.on_ai_symbol_changed)
        control_layout.addWidget(QLabel("交易对:"), 0, 0)
        control_layout.addWidget(self.ai_trading_symbol_combo, 0, 1)
        
        # 交易数量设置
        self.trade_amount_input = QDoubleSpinBox()
        self.trade_amount_input.setRange(0.01, 1000)  # 修改范围为合约数量范围
        self.trade_amount_input.setDecimals(2)  # 使用2位小数
        self.trade_amount_input.setValue(0.01)  # 默认值为最小交易量
        self.trade_amount_input.setSingleStep(0.01)  # 步进值为0.01
        control_layout.addWidget(QLabel("合约数量(张):"), 1, 0)
        control_layout.addWidget(self.trade_amount_input, 1, 1)
        
        # 添加订单类型选择
        self.order_type_layout = QHBoxLayout()
        self.market_order_radio = QRadioButton("市价单")
        self.market_order_radio.setChecked(True)  # 默认选中市价单
        self.limit_order_radio = QRadioButton("限价单")
        
        # 设置样式
        order_type_style = """
            QRadioButton {
                color: #E6E8EA;
                font-size: 14px;
                spacing: 5px;
            }
            QRadioButton::indicator {
                width: 16px;
                height: 16px;
                border-radius: 8px;
                border: 1px solid #2a2e30;
            }
            QRadioButton::indicator:checked {
                background-color: #F0B90B;
                border: 1px solid #F0B90B;
            }
        """
        self.market_order_radio.setStyleSheet(order_type_style)
        self.limit_order_radio.setStyleSheet(order_type_style)
        
        self.order_type_layout.addWidget(self.market_order_radio)
        self.order_type_layout.addWidget(self.limit_order_radio)
        self.order_type_layout.addStretch(1)
        
        control_layout.addWidget(QLabel("订单类型:"), 2, 0)
        control_layout.addLayout(self.order_type_layout, 2, 1)
        
        # 添加入场价格选项
        self.price_option_layout = QHBoxLayout()
        self.use_ai_price_radio = QRadioButton("使用AI分析价格")
        self.use_ai_price_radio.setChecked(True)  # 默认选中
        self.use_current_price_radio = QRadioButton("使用当前市价")
        
        self.use_ai_price_radio.setStyleSheet(order_type_style)
        self.use_current_price_radio.setStyleSheet(order_type_style)
        
        self.price_option_layout.addWidget(self.use_ai_price_radio)
        self.price_option_layout.addWidget(self.use_current_price_radio)
        self.price_option_layout.addStretch(1)
        
        control_layout.addWidget(QLabel("入场价格:"), 3, 0)
        control_layout.addLayout(self.price_option_layout, 3, 1)
        
        # 杠杆设置
        self.ai_leverage_spinbox = QSpinBox()
        self.ai_leverage_spinbox.setRange(1, 125)  # OKX支持的杠杆范围
        self.ai_leverage_spinbox.setValue(1)  # 默认值
        self.ai_leverage_spinbox.setSuffix("×")
        self.ai_leverage_spinbox.setStyleSheet("""
            QSpinBox {
                padding: 5px;
                border: 1px solid #333333;
                border-radius: 4px;
                background-color: #2A2A2A;
                color: #FFFFFF;
                font-size: 14px;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                width: 20px;
                background-color: #333333;
            }
            QSpinBox::up-button:hover, QSpinBox::down-button:hover {
                background-color: #444444;
            }
        """)
        # 添加杠杆倍数变化的处理函数
        self.ai_leverage_spinbox.valueChanged.connect(self.on_leverage_changed)
        control_layout.addWidget(QLabel("杠杆倍数:"), 4, 0)
        control_layout.addWidget(self.ai_leverage_spinbox, 4, 1)
        
        # 自动交易开关
        self.auto_trading_enabled = False
        self.auto_trading_button = QPushButton("启动自动交易")
        self.auto_trading_button.setProperty("type", "primary")
        self.auto_trading_button.setStyleSheet("""
            QPushButton {
                background-color: #F0B90B;
                color: #0B0E11;
                font-weight: bold;
                padding: 12px;
                border-radius: 4px;
                font-size: 15px;
                min-height: 45px;
            }
            QPushButton:hover {
                background-color: #F8D33A;
            }
            QPushButton:disabled {
                background-color: #5E5A3C;
                color: #848E9C;
            }
        """)
        self.auto_trading_button.clicked.connect(self.toggle_auto_trading)
        control_layout.addWidget(self.auto_trading_button, 5, 0, 1, 2)
        
        control_group.setLayout(control_layout)
        scroll_content_layout.addWidget(control_group)
        
        # AI交易日志
        log_group = QGroupBox("交易日志")
        log_layout = QVBoxLayout()
        log_layout.setContentsMargins(15, 25, 15, 15)
        log_layout.setSpacing(10)
        
        log_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 15px;
                margin-top: 1.5em;
                padding-top: 15px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 8px;
                color: #F0B90B;
            }
        """)
        
        # 日志文本区域 - 使用线程安全的文本编辑器
        self.trading_log = ThreadSafeTextEdit()
        self.trading_log.setReadOnly(True)
        self.trading_log.setStyleSheet("""
            QTextEdit {
                background-color: #14151a;
                color: #E6E8EA;
                border: 1px solid #2a2e30;
                border-radius: 4px;
                padding: 12px;
                font-family: "Consolas", "Monaco", monospace;
                font-size: 12px;
                line-height: 1.5;
            }
            QTextEdit:focus {
                border: 1px solid #35383c;
            }
        """)
        # 设置最小高度
        self.trading_log.setMinimumHeight(220)
        log_layout.addWidget(self.trading_log)
        
        # 添加按钮布局
        log_button_layout = QHBoxLayout()
        log_button_layout.setSpacing(10)
        
        # 保存日志按钮
        self.save_log_button = QPushButton("保存日志")
        self.save_log_button.setProperty("type", "neutral")
        self.save_log_button.setStyleSheet("""
            QPushButton {
                background-color: #2a2e30;
                color: #E6E8EA;
                border: none;
                border-radius: 4px;
                padding: 8px 15px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #35383c;
            }
        """)
        self.save_log_button.clicked.connect(self.save_trading_log)
        log_button_layout.addWidget(self.save_log_button)
        
        # 删除日志按钮
        self.delete_log_button = QPushButton("删除日志")
        self.delete_log_button.setProperty("type", "delete")
        self.delete_log_button.clicked.connect(self.delete_trading_log)
        log_button_layout.addWidget(self.delete_log_button)
        
        log_layout.addLayout(log_button_layout)
        log_group.setLayout(log_layout)
        scroll_content_layout.addWidget(log_group)
        
        # 设置滚动内容
        scroll_area.setWidget(scroll_content)
        
        # 将滚动区域添加到主布局
        ai_trading_layout.addWidget(scroll_area)
        
        right_panel.addTab(ai_trading_tab, "AI交易")
        
        # 新闻分析标签页 -> 改为API配置标签页
        api_tab = QWidget()
        api_layout = QVBoxLayout(api_tab)
        api_layout.setContentsMargins(20, 20, 20, 20)
        api_layout.setSpacing(20)
        
        # API配置面板
        api_group = QGroupBox("API密钥配置")
        api_layout_grid = QGridLayout()
        api_layout_grid.setVerticalSpacing(20)
        api_layout_grid.setContentsMargins(20, 30, 20, 20)
        
        # News API 配置
        api_layout_grid.addWidget(QLabel("News API密钥:"), 0, 0)
        self.news_api_key = QLineEdit()
        self.news_api_key.setPlaceholderText("请输入News API密钥")
        self.news_api_key.setText(os.getenv('NEWS_API_KEY', ''))
        api_layout_grid.addWidget(self.news_api_key, 0, 1)
        
        news_button_layout = QHBoxLayout()
        news_button_layout.setSpacing(10)
        
        save_news_button = QPushButton("保存")
        save_news_button.setProperty("type", "neutral")
        save_news_button.clicked.connect(self.save_news_api)
        
        test_news_button = QPushButton("测试")
        test_news_button.setProperty("type", "neutral")
        test_news_button.clicked.connect(self.test_news_api)
        
        delete_news_button = QPushButton("删除")
        delete_news_button.setProperty("type", "delete")
        delete_news_button.clicked.connect(self.delete_news_api)
        
        news_button_layout.addWidget(save_news_button)
        news_button_layout.addWidget(test_news_button)
        news_button_layout.addWidget(delete_news_button)
        api_layout_grid.addLayout(news_button_layout, 0, 2)
        
        # DeepSeek AI API 配置
        api_layout_grid.addWidget(QLabel("DeepSeek AI API密钥:"), 1, 0)
        self.ai_api_key = QLineEdit()
        self.ai_api_key.setPlaceholderText("请输入DeepSeek AI API密钥")
        self.ai_api_key.setText(os.getenv('DEEPSEEK_API_KEY', ''))
        api_layout_grid.addWidget(self.ai_api_key, 1, 1)
        
        ai_button_layout = QHBoxLayout()
        ai_button_layout.setSpacing(10)
        
        save_ai_button = QPushButton("保存")
        save_ai_button.setProperty("type", "neutral")
        save_ai_button.clicked.connect(self.save_ai_api)
        
        test_ai_button = QPushButton("测试")
        test_ai_button.setProperty("type", "neutral")
        test_ai_button.clicked.connect(self.test_ai_api)
        
        delete_ai_button = QPushButton("删除")
        delete_ai_button.setProperty("type", "delete")
        delete_ai_button.clicked.connect(self.delete_ai_api)
        
        ai_button_layout.addWidget(save_ai_button)
        ai_button_layout.addWidget(test_ai_button)
        ai_button_layout.addWidget(delete_ai_button)
        api_layout_grid.addLayout(ai_button_layout, 1, 2)
        
        # API说明文本
        api_info = QLabel("配置API密钥用于获取市场新闻和AI分析。这些API密钥将存储在本地环境变量中，不会上传到任何服务器。")
        api_info.setWordWrap(True)
        api_info.setStyleSheet("color: #848E9C; font-size: 13px; margin-top: 20px;")
        api_layout_grid.addWidget(api_info, 2, 0, 1, 3)
        
        api_group.setLayout(api_layout_grid)
        api_layout.addWidget(api_group)
        
        # 添加说明
        instruction_group = QGroupBox("使用说明")
        instruction_layout = QVBoxLayout()
        instruction_layout.setContentsMargins(20, 20, 20, 20)
        
        instruction_text = QTextEdit()
        instruction_text.setReadOnly(True)
        instruction_text.setStyleSheet("""
            QTextEdit {
                background-color: #14151a;
                color: #E6E8EA;
                border: 1px solid #2a2e30;
                border-radius: 4px;
                padding: 15px;
                font-size: 13px;
                line-height: 1.5;
            }
        """)
        
        instruction_text.setHtml("""
            <h3 style="color: #F0B90B;">API配置说明</h3>
            <p>为了使用量化交易机器人的全部功能，需要配置以下API：</p>
            <ul>
                <li><b>News API</b> - 用于获取加密货币市场相关新闻</li>
                <li><b>DeepSeek AI API</b> - 用于分析市场数据和新闻，生成交易信号</li>
            </ul>
            <h3 style="color: #F0B90B;">如何获取API密钥</h3>
            <p><b>News API</b>: 访问 <a href="https://newsapi.org" style="color:#E6E8EA;">newsapi.org</a> 注册账号并获取API密钥</p>
            <p><b>DeepSeek AI API</b>: 访问 <a href="https://platform.deepseek.com" style="color:#E6E8EA;">platform.deepseek.com</a> 注册账号并创建API密钥</p>
        """)
        
        instruction_layout.addWidget(instruction_text)
        instruction_group.setLayout(instruction_layout)
        api_layout.addWidget(instruction_group)
        
        # 添加弹性空间
        api_layout.addStretch(1)
        
        right_panel.addTab(api_tab, "API配置")
        
        # 将面板添加到分割器
        main_splitter.addWidget(left_panel)
        main_splitter.addWidget(right_panel)
        main_splitter.setStretchFactor(0, 2)  # 左侧占2/3
        main_splitter.setStretchFactor(1, 1)  # 右侧占1/3
        
        layout.addWidget(main_splitter)
        
    def update_market_data(self):
        """优化后的市场数据更新方法"""
        try:
            # 防止重复更新
            if self.is_updating:
                return
            
            # 检查更新时间间隔
            current_time = time.time()
            if current_time - self.last_update_time < self.update_interval:
                return
            
            self.is_updating = True
            self.last_update_time = current_time
            
            # 使用线程池执行数据获取任务
            worker = Worker(self._fetch_market_data)
            worker.signals.result.connect(self._process_market_data)
            worker.signals.finished.connect(self._update_finished)
            self.thread_pool.start(worker)
            
        except Exception as e:
            self.log_trading(f"更新市场数据失败: {str(e)}")
            self.is_updating = False

    def _fetch_market_data(self):
        """在后台线程中获取市场数据"""
        try:
            symbol = self.symbol_combo.currentText()
            # 确保使用一致的格式获取永续合约数据
            base_quote = symbol.split('/')
            base, quote = base_quote[0], base_quote[1]
            okx_symbol = f"{base}-{quote}-SWAP"
            
            # 获取当前选择的时间周期
            timeframe = '15m'  # 默认15分钟
            if hasattr(self, 'chart_widget') and hasattr(self.chart_widget, 'timeframe_combo'):
                selected_tf = self.chart_widget.timeframe_combo.currentText()
                if selected_tf == '5分钟':
                    timeframe = '5m'
                elif selected_tf == '15分钟':
                    timeframe = '15m'
                elif selected_tf == '1小时':
                    timeframe = '1h'
                elif selected_tf == '4小时':
                    timeframe = '4h'
                elif selected_tf == '1天':
                    timeframe = '1d'
            
            # 使用缓存减少API调用
            cache_key = f"{okx_symbol}_{timeframe}"
            if cache_key in self.data_cache and self.data_cache[cache_key].is_valid():
                return self.data_cache[cache_key].data
                
            # 根据时间周期调整获取的K线数量
            limit = 100
            if timeframe == '1h':
                limit = 120
            elif timeframe == '4h':
                limit = 150
            elif timeframe == '1d':
                limit = 180
                
            # 获取所有需要的数据
            data = {
                'ohlcv': self.exchange.fetch_ohlcv(okx_symbol, timeframe, limit=limit),
                'ticker': self.exchange.fetch_ticker(okx_symbol),
                'balance': self.exchange.fetch_balance(params={'instType': 'SWAP'}),
                'positions': self.exchange.fetch_positions(symbols=[okx_symbol], params={'instType': 'SWAP'}),
                'orders': self.exchange.fetch_open_orders(params={'instType': 'SWAP'})
            }
            
            # 更新缓存
            self.data_cache[cache_key] = CachedData(data)
            return data
            
        except Exception as e:
            raise Exception(f"获取市场数据失败: {str(e)}")

    def _process_market_data(self, data):
        """处理获取到的市场数据"""
        try:
            if not data:
                return
                
            # 更新K线图
            if data.get('ohlcv'):
                self.chart_widget.update_data(data['ohlcv'])
                
            # 更新价格显示
            if data.get('ticker'):
                self.update_ai_trading_price()
                
            # 更新账户信息
            if data.get('balance'):
                self.update_account_table(data['balance'])
                
            # 更新持仓信息
            if data.get('positions'):
                self.update_positions_table(data['positions'])
                
            # 更新订单信息
            if data.get('orders'):
                self.update_orders_table(data['orders'])
                
        except Exception as e:
            self.log_trading(f"处理市场数据失败: {str(e)}")

    def _update_finished(self):
        """更新完成后的处理"""
        self.is_updating = False

    def update_account_table(self, balance):
        """更新账户信息表格"""
        self.account_table.setRowCount(0)
        
        # 计算USDT和BTC总资产（新增）
        total_usdt = 0
        total_btc = 0
        total_pnl = 0
        
        for currency in balance['total']:
            if balance['total'][currency] > 0:
                # 更新表格
                row = self.account_table.rowCount()
                self.account_table.insertRow(row)
                
                # 设置资产名称并添加图标占位符
                currency_item = QTableWidgetItem(currency)
                currency_item.setForeground(QColor('#F0B90B') if currency in ['USDT', 'BTC', 'ETH'] else QColor('#E6E8EA'))
                self.account_table.setItem(row, 0, currency_item)
                
                # 为可用、已用、总计添加数值，并设置数字格式
                free_value = balance['free'][currency]
                used_value = balance['used'][currency]
                total_value = balance['total'][currency]
                
                # 设置精度：主要币种8位小数，USDT 2位小数，其他4位小数
                if currency == 'BTC' or currency == 'ETH':
                    precision = 8
                elif currency == 'USDT':
                    precision = 2 
                    total_usdt += total_value  # 累计USDT总资产
                else:
                    precision = 4
                
                self.account_table.setItem(row, 1, QTableWidgetItem(f"{free_value:.{precision}f}"))
                self.account_table.setItem(row, 2, QTableWidgetItem(f"{used_value:.{precision}f}"))
                
                # 设置总计列，添加颜色
                total_item = QTableWidgetItem(f"{total_value:.{precision}f}")
                if total_value > 0:
                    total_item.setForeground(QColor('#E6E8EA'))
                self.account_table.setItem(row, 3, total_item)
        
        # 更新资产摘要显示（新增）
        self.usdt_value.setText(f"{total_usdt:.2f}")
        self.btc_value.setText(f"{total_btc:.8f}")
        
        # 从持仓中获取未实现盈亏
        try:
            for row in range(self.positions_table.rowCount()):
                pnl_text = self.positions_table.item(row, 4).text()
                try:
                    pnl_value = float(pnl_text)
                    total_pnl += pnl_value
                except:
                    pass
                    
            # 设置未实现盈亏，包括正负颜色
            self.pnl_value.setText(f"{total_pnl:.2f}")
            if total_pnl >= 0:
                self.pnl_value.setStyleSheet("color: #2EBD85; font-size: 22px; font-weight: bold;")
            else:
                self.pnl_value.setStyleSheet("color: #F23645; font-size: 22px; font-weight: bold;")
        except Exception as e:
            self.log_trading(f"更新未实现盈亏失败: {str(e)}")

    def update_positions_table(self, positions):
        """更新持仓信息表格"""
        try:
            self.positions_table.setRowCount(0)
            
            # 确保positions是列表
            if not isinstance(positions, list):
                positions = [positions]
                
            for position in positions:
                # 检查是否有实际持仓
                size = float(position.get('contracts', 0))
                if size != 0:  # 修改判断条件，检查持仓量是否为0
                    row = self.positions_table.rowCount()
                    self.positions_table.insertRow(row)
                    
                    # 获取合约信息
                    symbol = position.get('symbol', '')
                    entry_price = float(position.get('entryPrice', 0))
                    mark_price = float(position.get('markPrice', 0))
                    unrealized_pnl = float(position.get('unrealizedPnl', 0))
                    side = "多" if position.get('side') == 'long' else "空"
                    
                    # 设置表格内容
                    self.positions_table.setItem(row, 0, QTableWidgetItem(f"{symbol} ({side})"))
                    self.positions_table.setItem(row, 1, QTableWidgetItem(f"{size:.4f}"))
                    self.positions_table.setItem(row, 2, QTableWidgetItem(f"{entry_price:.2f}"))
                    self.positions_table.setItem(row, 3, QTableWidgetItem(f"{mark_price:.2f}"))
                    
                    # 设置PnL颜色
                    pnl_item = QTableWidgetItem(f"{unrealized_pnl:.2f}")
                    pnl_item.setForeground(QColor('#2EBD85') if unrealized_pnl >= 0 else QColor('#F23645'))
                    self.positions_table.setItem(row, 4, pnl_item)
                
        except Exception as e:
            self.log_trading(f"更新持仓信息失败: {str(e)}")

    def update_orders_table(self, orders):
        """更新委托订单表格"""
        try:
            self.orders_table.setRowCount(0)
            
            # 调试日志
            # self.log_trading(f"更新委托订单表格: 收到 {len(orders)} 个订单")
            
            for order in orders:
                row = self.orders_table.rowCount()
                self.orders_table.insertRow(row)
                
                # 获取订单信息
                order_id = order.get('id', '')
                symbol = order.get('symbol', '')
                side = "多" if order.get('side') == 'buy' else "空"
                order_type = order.get('type', '').upper()
                price = float(order.get('price', 0))
                amount = float(order.get('amount', 0))
                status = self.translate_order_status(order.get('status', ''))
                
                # 调试日志 - 只记录每个订单的基本信息
                # self.log_trading(f"添加订单: ID={order_id}, 交易对={symbol}, 状态={status}")
                
                # 设置表格内容
                self.orders_table.setItem(row, 0, QTableWidgetItem(order_id))
                self.orders_table.setItem(row, 1, QTableWidgetItem(symbol))
                
                # 设置方向单元格颜色
                side_item = QTableWidgetItem(side)
                side_item.setForeground(QColor('#2EBD85') if side == "多" else QColor('#F23645'))
                self.orders_table.setItem(row, 2, side_item)
                
                self.orders_table.setItem(row, 3, QTableWidgetItem(order_type))
                self.orders_table.setItem(row, 4, QTableWidgetItem(f"{price:.2f}"))
                self.orders_table.setItem(row, 5, QTableWidgetItem(f"{amount:.4f}"))
                self.orders_table.setItem(row, 6, QTableWidgetItem(status))
                
        except Exception as e:
            self.log_trading(f"更新委托订单表格失败: {str(e)}")

    def translate_order_status(self, status):
        """翻译订单状态"""
        status_map = {
            'open': '未成交',
            'closed': '已成交',
            'canceled': '已取消',
            'expired': '已过期',
            'rejected': '已拒绝',
            'pending': '等待中'
        }
        return status_map.get(status, status)

    def cancel_selected_orders(self):
        """取消选中的订单"""
        try:
            selected_rows = set(item.row() for item in self.orders_table.selectedItems())
            if not selected_rows:
                self.show_message_signal.emit("提示", "请先选择要取消的订单", "warning")
                return
                
            # 确认取消
            reply = QMessageBox.question(
                self, 
                "确认取消",
                f"确定要取消选中的 {len(selected_rows)} 个订单吗？",
                QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
            )
            
            if reply == QMessageBox.StandardButton.Yes:
                for row in selected_rows:
                    order_id = self.orders_table.item(row, 0).text()
                    symbol = self.orders_table.item(row, 1).text()
                    
                    try:
                        self.exchange.cancel_order(order_id, symbol)
                        self.log_trading(f"已取消订单 {order_id}")
                    except Exception as e:
                        self.log_trading(f"取消订单 {order_id} 失败: {str(e)}")
                
                # 刷新订单列表 - 等待1秒给交易所一些处理时间
                QTimer.singleShot(1000, self.refresh_order_data)
                
        except Exception as e:
            self.log_trading(f"取消订单操作失败: {str(e)}")
            
    def refresh_order_data(self):
        """手动刷新订单数据"""
        try:
            self.log_trading("正在刷新订单数据...")
            # 获取所有订单信息 
            orders = self.exchange.fetch_open_orders(params={'instType': 'SWAP'})
            self.update_orders_table(orders)
            self.log_trading("订单数据刷新完成")
        except Exception as e:
            self.log_trading(f"刷新订单数据失败: {str(e)}")

    def on_symbol_changed(self, symbol):
        """交易对改变时的处理"""
        # 更新K线图
        try:
            ohlcv = self.exchange.fetch_ohlcv(symbol, '1m', limit=100)
            self.chart_widget.update_data(ohlcv)
            
            # 同步更新AI交易标签页中的交易对选择
            if hasattr(self, 'ai_trading_symbol_combo') and symbol != self.ai_trading_symbol_combo.currentText():
                self.ai_trading_symbol_combo.blockSignals(True)  # 阻止触发信号
                self.ai_trading_symbol_combo.setCurrentText(symbol)  # 设置相同的交易对
                self.ai_trading_symbol_combo.blockSignals(False)  # 恢复信号处理
                
                # 更新AI交易界面的实时价格
                self.update_ai_trading_price()
            
            # 同步更新新闻分析标签页中的交易对选择
            if hasattr(self, 'news_symbol_combo') and symbol != self.news_symbol_combo.currentText():
                self.news_symbol_combo.blockSignals(True)  # 阻止触发信号
                self.news_symbol_combo.setCurrentText(symbol)  # 设置相同的交易对
                self.news_symbol_combo.blockSignals(False)  # 恢复信号处理
        except Exception as e:
            self.log_trading(f"交易对变更处理失败: {str(e)}", level='error')
            
    def save_news_api(self):
        """保存News API配置"""
        try:
            api_key = self.news_api_key.text()
            
            if not api_key:
                self.show_message_signal.emit("警告", "请输入News API密钥！", "warning")
                return
                
            # 读取现有的环境变量
            env_vars = {}
            if os.path.exists('.env'):
                load_dotenv()
                env_vars = {
                    'OKX_API_KEY': os.getenv('OKX_API_KEY', ''),
                    'OKX_SECRET_KEY': os.getenv('OKX_SECRET_KEY', ''),
                    'OKX_PASSPHRASE': os.getenv('OKX_PASSPHRASE', ''),
                    'DEEPSEEK_API_KEY': os.getenv('DEEPSEEK_API_KEY', '')
                }
            
            # 更新News API密钥
            env_vars['NEWS_API_KEY'] = api_key
            
            # 保存所有环境变量
            with open('.env', 'w') as f:
                for key, value in env_vars.items():
                    if value:
                        f.write(f"{key}={value}\n")
                
            self.show_message_signal.emit("成功", "News API配置已保存！", "info")
            
        except Exception as e:
            self.show_message_signal.emit("错误", f"保存News API配置失败: {str(e)}", "error")
            
    def delete_news_api(self):
        """删除News API配置"""
        try:
            reply = QMessageBox.question(self, "确认", "确定要删除News API配置吗？",
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            
            if reply == QMessageBox.StandardButton.Yes:
                # 读取现有的环境变量
                env_vars = {}
                if os.path.exists('.env'):
                    load_dotenv()
                    env_vars = {
                        'OKX_API_KEY': os.getenv('OKX_API_KEY', ''),
                        'OKX_SECRET_KEY': os.getenv('OKX_SECRET_KEY', ''),
                        'OKX_PASSPHRASE': os.getenv('OKX_PASSPHRASE', ''),
                        'DEEPSEEK_API_KEY': os.getenv('DEEPSEEK_API_KEY', '')
                    }
                    
                    # 删除News API密钥
                    env_vars.pop('NEWS_API_KEY', None)
                    
                    # 保存其他环境变量
                    with open('.env', 'w') as f:
                        for key, value in env_vars.items():
                            if value:
                                f.write(f"{key}={value}\n")
                
                self.news_api_key.clear()
                self.show_message_signal.emit("成功", "News API配置已删除！", "info")
                
        except Exception as e:
            self.show_message_signal.emit("错误", f"删除News API配置失败: {str(e)}", "error")
            
    def save_ai_api(self):
        """保存DeepSeek AI API配置"""
        try:
            api_key = self.ai_api_key.text()
            
            if not api_key:
                self.show_message_signal.emit("警告", "请输入DeepSeek AI API密钥！", "warning")
                return
                
            # 读取现有的环境变量
            env_vars = {}
            if os.path.exists('.env'):
                load_dotenv()
                env_vars = {
                    'OKX_API_KEY': os.getenv('OKX_API_KEY', ''),
                    'OKX_SECRET_KEY': os.getenv('OKX_SECRET_KEY', ''),
                    'OKX_PASSPHRASE': os.getenv('OKX_PASSPHRASE', ''),
                    'NEWS_API_KEY': os.getenv('NEWS_API_KEY', '')
                }
            
            # 更新DeepSeek AI API密钥
            env_vars['DEEPSEEK_API_KEY'] = api_key
            
            # 保存所有环境变量
            with open('.env', 'w') as f:
                for key, value in env_vars.items():
                    if value:
                        f.write(f"{key}={value}\n")
                
            self.show_message_signal.emit("成功", "DeepSeek AI API配置已保存！", "info")
            
        except Exception as e:
            self.show_message_signal.emit("错误", f"保存DeepSeek AI API配置失败: {str(e)}", "error")
            
    def delete_ai_api(self):
        """删除DeepSeek AI API配置"""
        try:
            reply = QMessageBox.question(self, "确认", "确定要删除DeepSeek AI API配置吗？",
                                       QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No)
            
            if reply == QMessageBox.StandardButton.Yes:
                # 读取现有的环境变量
                env_vars = {}
                if os.path.exists('.env'):
                    load_dotenv()
                    env_vars = {
                        'OKX_API_KEY': os.getenv('OKX_API_KEY', ''),
                        'OKX_SECRET_KEY': os.getenv('OKX_SECRET_KEY', ''),
                        'OKX_PASSPHRASE': os.getenv('OKX_PASSPHRASE', ''),
                        'NEWS_API_KEY': os.getenv('NEWS_API_KEY', '')
                    }
                    
                    # 删除DeepSeek AI API密钥
                    env_vars.pop('DEEPSEEK_API_KEY', None)
                    
                    # 保存其他环境变量
                    with open('.env', 'w') as f:
                        for key, value in env_vars.items():
                            if value:
                                f.write(f"{key}={value}\n")
                
                self.ai_api_key.clear()
                self.show_message_signal.emit("成功", "DeepSeek AI API配置已删除！", "info")
                
        except Exception as e:
            self.show_message_signal.emit("错误", f"删除DeepSeek AI API配置失败: {str(e)}", "error")

    def analyze_news(self):
        """分析新闻并生成AI预测"""
        try:
            # 检查API密钥
            if not self.news_api_key.text() or not self.ai_api_key.text():
                self.show_message_signal.emit("警告", "请先在API设置中配置News API和DeepSeek AI API密钥！", "warning")
                return
                
            self.setEnabled(False)  # 禁用界面
            QApplication.processEvents()  # 处理界面事件
            
            # 获取选择的货币和时间范围
            symbol = self.news_symbol_combo.currentText()
            time_range = self.time_range_combo.currentText()
            
            # 设置时间范围
            if time_range == '最近24小时':
                days = 1
            elif time_range == '最近3天':
                days = 3
            else:
                days = 7
                
            from_date = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d')
            
            # 更新状态
            self.analysis_text.setText("正在获取新闻数据...")
            QApplication.processEvents()
            
            # 获取新闻数据，增加更多新闻源和关键词
            news_sources = [
                {
                    'url': 'https://newsapi.org/v2/everything',
                    'params': {
                        'q': f'(Trump OR Federal Reserve OR SEC OR regulation OR "crypto regulation" OR "CFTC" OR "Treasury Department" OR "central bank" OR "Yellen" OR "institutional adoption" OR "Blackrock" OR "JP Morgan" OR "Goldman Sachs" OR "digital currency") AND ({symbol} OR cryptocurrency OR "digital asset" OR blockchain)',
                        'sortBy': 'publishedAt',
                        'language': 'en',
                        'pageSize': 6,
                        'apiKey': self.news_api_key.text()
                    }
                },
                {
                    'url': 'https://newsapi.org/v2/everything',
                    'params': {
                        'q': f'(market analysis OR price prediction OR volatility OR "market sentiment" OR "trading volume" OR "bullish sentiment" OR "bearish outlook") AND ({symbol} OR cryptocurrency)',
                        'sortBy': 'publishedAt',
                        'language': 'en',
                        'pageSize': 6,
                        'apiKey': self.news_api_key.text()
                    }
                }
            ]
            
            all_articles = []
            for source in news_sources:
                try:
                    news_response = requests.get(source['url'], params=source['params'], timeout=10)
                    if news_response.status_code == 200:
                        news_data = news_response.json()
                        articles = news_data.get('articles', [])
                        all_articles.extend(articles)
                except Exception as e:
                    self.log_trading(f"获取新闻源失败: {str(e)}")
            
            if not all_articles:
                self.log_trading("未找到相关新闻")
                return None
            
            # 新闻情感分析
            news_sentiment = 0
            news_summary = "最新加密货币相关新闻摘要：\n\n"
            
            for article in all_articles[:10]:  # 分析前10条新闻
                title = article['title']
                description = article.get('description', '')
                content = f"{title} {description}"
                
                # 情感分析关键词
                positive_keywords = ['bullish', 'surge', 'rise', 'gain', 'positive', 'growth', 'adoption', 'approval', 'institutional investment', 'breakthrough', 'rally', 'endorsement', 'partnership', 'launch', 'innovation']
                negative_keywords = ['bearish', 'crash', 'drop', 'fall', 'negative', 'decline', 'ban', 'reject', 'regulation concerns', 'investigation', 'lawsuit', 'hack', 'security breach', 'restrictions', 'vulnerability', 'criticism']
                
                # 计算情感分数，引入关键词权重
                sentiment_score = 0
                
                # 高权重关键词
                high_impact_positive = ['institutional adoption', 'ETF approval', 'major partnership', 'regulatory clarity', 'mainstream adoption']
                high_impact_negative = ['trading ban', 'major hack', 'SEC lawsuit', 'criminal charges', 'severe restriction']
                
                # 检查高权重关键词
                for keyword in high_impact_positive:
                    if keyword.lower() in content.lower():
                        sentiment_score += 2  # 高权重积极关键词计2分
                
                for keyword in high_impact_negative:
                    if keyword.lower() in content.lower():
                        sentiment_score -= 2  # 高权重消极关键词计-2分
                
                # 检查普通关键词
                for keyword in positive_keywords:
                    if keyword.lower() in content.lower():
                        sentiment_score += 1
                        
                for keyword in negative_keywords:
                    if keyword.lower() in content.lower():
                        sentiment_score -= 1
                
                news_sentiment += sentiment_score
                
                # 添加到新闻摘要
                news_summary += f"- {title}\n"
                if description:
                    news_summary += f"  摘要: {description[:200]}...\n"
                news_summary += f"  情感倾向: {'看多' if sentiment_score > 0 else '看空' if sentiment_score < 0 else '中性'}\n\n"
            
            # 计算整体新闻情感，增加权重影响
            avg_news_sentiment = news_sentiment / len(all_articles[:10])
            # 降低中性判断阈值，使系统更容易得出明确的多空信号
            news_trend = "看多" if avg_news_sentiment > 0.4 else "看空" if avg_news_sentiment < -0.4 else "中性"
            
            # 记录情感分析结果
            self.log_trading(f"新闻情感分析：整体得分 {avg_news_sentiment:.2f}，趋势判断 {news_trend}", level='info')
            self.log_trading(f"分析了 {len(all_articles[:10])} 条新闻，包含监管动态和机构参与相关内容", level='info')
            
            # 初始化趋势强度和信号列表
            trend_strength = 0
            trend_signals = []
            
            # 将新闻情感纳入趋势强度计算，增加权重
            if news_trend == "看多":
                trend_strength += 2  # 将权重从1增加到2
                trend_signals.append("新闻情感明显偏多")
            elif news_trend == "看空":
                trend_strength -= 2  # 将权重从1增加到2
                trend_signals.append("新闻情感明显偏空")
            
            # 修改获取市场数据部分，使用正确的交易对格式
            swap_symbol = f"{symbol}-USDT-SWAP"  # 使用永续合约格式
            ticker = self.exchange.fetch_ticker(swap_symbol)
            current_price = ticker['last']
            
            # 获取K线数据也使用永续合约格式
            ohlcv = self.exchange.fetch_ohlcv(swap_symbol, '15m', limit=100)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            
            # 添加价格精度处理
            current_price = round(current_price, 2)
            volatility = round(abs((current_price - self.last_price) / self.last_price * 100) if hasattr(self, 'last_price') else 0, 2)
            self.last_price = current_price
            
            # 更新状态
            self.analysis_text.setText("正在生成AI分析...")
            QApplication.processEvents()
            
            # 计算技术指标
            ohlcv = self.exchange.fetch_ohlcv(f"{symbol}/USDT", '15m', limit=100)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            close_prices = df['close'].values
            rsi = talib.RSI(close_prices, timeperiod=14)
            macd, signal, _ = talib.MACD(close_prices)
            upper, middle, lower = talib.BBANDS(close_prices)
            
            # 初始化AI提示中需要的其他变量
            market_state = "RANGING_WEAK_TREND"  # 默认市场状态
            adx_value = 0.0
            plus_di_value = 0.0
            minus_di_value = 0.0
            current_adx_threshold = getattr(self, 'adx_threshold', 25)
            
            # 计算其他需要的技术指标
            ema20 = talib.EMA(close_prices, timeperiod=getattr(self, 'ema_short', 20))
            ema50 = talib.EMA(close_prices, timeperiod=getattr(self, 'ema_medium', 50))
            stoch_k, stoch_d = talib.STOCH(df['high'].values, df['low'].values, close_prices)
            adx = talib.ADX(df['high'].values, df['low'].values, close_prices, timeperiod=getattr(self, 'adx_period', 14))
            di_plus = talib.PLUS_DI(df['high'].values, df['low'].values, close_prices, timeperiod=getattr(self, 'adx_period', 14))
            di_minus = talib.MINUS_DI(df['high'].values, df['low'].values, close_prices, timeperiod=getattr(self, 'adx_period', 14))
            cci = talib.CCI(df['high'].values, df['low'].values, close_prices, timeperiod=getattr(self, 'cci_period', 20))
            mfi = talib.MFI(df['high'].values, df['low'].values, close_prices, df['volume'].values, timeperiod=getattr(self, 'mfi_period', 14))
            
            # 判断市场状态
            if adx[-1] > current_adx_threshold:
                if di_plus[-1] > di_minus[-1]:
                    market_state = "STRONG_UPTREND"
                elif di_minus[-1] > di_plus[-1]:
                    market_state = "STRONG_DOWNTREND"
            else:
                market_state = "RANGING_WEAK_TREND"
            
            adx_value = adx[-1]
            plus_di_value = di_plus[-1]
            minus_di_value = di_minus[-1]
            
            # 准备AI分析提示
            prompt = f"""作为一个专业的加密货币分析师，请基于以下信息进行详细分析，并严格按照指定格式输出结果。
请特别注意：
1. 趋势判断必须明确且及时，不要犹豫
2. 价格设置必须严格符合要求，不得有任何偏差
3. 重点关注特朗普和美联储新闻对加密货币的影响

当前市场信息：
- 货币对：{symbol}/USDT
- 当前价格：{current_price}
- **判定市场状态**：{market_state} (基于ADX({getattr(self, 'adx_period', 14)})={adx_value:.2f}, +DI={plus_di_value:.2f}, -DI={minus_di_value:.2f}, 阈值={current_adx_threshold})
- RSI({getattr(self, 'rsi_period', 14)}): {rsi[-1]:.2f} {'(超买)' if rsi[-1] > getattr(self, 'rsi_overbought', 70) else '(超卖)' if rsi[-1] < getattr(self, 'rsi_oversold', 30) else ''}
- MACD({getattr(self, 'macd_fast', 12)},{getattr(self, 'macd_slow', 26)},{getattr(self, 'macd_signal', 9)}): {macd[-1]:.2f} {'(金叉)' if macd[-1] > signal[-1] else '(死叉)'}
- 布林带({getattr(self, 'bb_period', 20)},{getattr(self, 'bb_std', 2.0)}): 上轨 {upper[-1]:.2f}, 中轨 {middle[-1]:.2f}, 下轨 {lower[-1]:.2f}
  {'(突破上轨)' if current_price > upper[-1] else '(突破下轨)' if current_price < lower[-1] else '(区间内)'}
- EMA: {getattr(self, 'ema_short', 20)}日 {ema20[-1]:.2f}, {getattr(self, 'ema_medium', 50)}日 {ema50[-1]:.2f}
  {'(黄金交叉)' if ema20[-1] > ema50[-1] and ema20[-2] <= ema50[-2] else '(死亡交叉)' if ema20[-1] < ema50[-1] and ema20[-2] >= ema50[-2] else ''}
- 随机指标: K({stoch_k[-1]:.2f}) D({stoch_d[-1]:.2f}) {'(超买区)' if stoch_k[-1] > getattr(self, 'stoch_overbought', 80) else '(超卖区)' if stoch_k[-1] < getattr(self, 'stoch_oversold', 20) else ''}
- ADX({getattr(self, 'adx_period', 14)}): {adx[-1]:.2f} (+DI: {di_plus[-1]:.2f}, -DI: {di_minus[-1]:.2f}) {'(趋势强)' if adx[-1] > getattr(self, 'adx_threshold', 25) else '(趋势弱)'}
- CCI({getattr(self, 'cci_period', 20)}): {cci[-1]:.2f} {'(超买)' if cci[-1] > getattr(self, 'cci_overbought', 100) else '(超卖)' if cci[-1] < getattr(self, 'cci_oversold', -100) else ''}
- MFI({getattr(self, 'mfi_period', 14)}): {mfi[-1]:.2f} {'(超买)' if mfi[-1] > getattr(self, 'mfi_overbought', 80) else '(超卖区)' if mfi[-1] < getattr(self, 'mfi_oversold', 20) else ''}

趋势判断参考：
1. 技术面分析：
   **请结合当前市场状态 ({market_state}) 解读以下指标：**
   - **强趋势 ({str(market_state == "STRONG_UPTREND" or market_state == "STRONG_DOWNTREND")})**：
     - 优先关注与趋势同向的信号（如MACD顺势交叉，价格沿布林带轨道运行）。
     - 振荡指标（RSI, Stochastic, CCI, MFI）的超买/超卖可能指示趋势持续，而非立即反转，需谨慎对待其反转信号。
   - **震荡/弱趋势 ({str(market_state == "RANGING_WEAK_TREND")})**：
     - 振荡指标的超买/超卖信号权重增加，可用于寻找潜在的反转点。
     - MACD和布林带的信号需警惕假突破。

   - RSI > {getattr(self, 'rsi_overbought', 70)} 且价格突破布林上轨，倾向看空 (震荡市更可靠)
   - RSI < {getattr(self, 'rsi_oversold', 30)} 且价格突破布林下轨，倾向看多 (震荡市更可靠)
   - MACD金叉且RSI回升，倾向看多
   - MACD死叉且RSI下降，倾向看空
   - EMA{getattr(self, 'ema_short', 20)}上穿EMA{getattr(self, 'ema_medium', 50)}形成黄金交叉，倾向看多
   - EMA{getattr(self, 'ema_short', 20)}下穿EMA{getattr(self, 'ema_medium', 50)}形成死亡交叉，倾向看空
   - ADX > {getattr(self, 'adx_threshold', 25)}表示趋势强，结合其他指标判断方向
   - 随机指标K值和D值在超买区（>{getattr(self, 'stoch_overbought', 80)}）且开始下降，倾向看空
   - 随机指标K值和D值在超卖区（<{getattr(self, 'stoch_oversold', 20)}）且开始上升，倾向看多

2. 新闻面分析：
   - 特朗普相关新闻：
     * 关注其对加密货币的态度和言论
     * 其政策主张对加密货币市场的潜在影响
     * 是否涉及对加密货币的监管态度
   
   - 美联储相关新闻：
     * 利率政策变化及预期
     * 通胀数据及应对措施
     * 货币政策声明对市场的影响
     * 美元走势对加密货币的影响

   - 监管动态：
     * SEC、CFTC等监管机构的政策发布与声明
     * 全球主要国家加密货币监管法规变化
     * 对交易所的监管措施与合规要求
     * 反洗钱和KYC政策的实施情况
   
   - 机构参与度：
     * 大型机构（如BlackRock、摩根大通、高盛等）投资加密货币的动向
     * ETF批准或拒绝的最新进展
     * 传统金融机构进入加密领域的战略
     * 机构持仓量的变化趋势

{news_summary}

请严格按照以下规则设置价格：

1. 如果建议做多：
   - 入场价必须接近但略低于当前价格（差距不超过0.5%）
   - 止盈价必须设置在入场价的0.5-1%上方（包含边界值）
   - 止损价必须设置在入场价的3-5%下方（包含3%和5%的边界值）

2. 如果建议做空：
   - 入场价必须接近但略高于当前价格（差距不超过0.5%）
   - 止盈价必须设置在入场价的0.5-1%下方（包含边界值）
   - 止损价必须设置在入场价的3-5%上方（包含3%和5%的边界值）

请按照以下格式输出：

市场趋势：[看多/看空]
入场价位：[具体数字] USDT
止盈价位：[具体数字] USDT
止损价位：[具体数字] USDT

详细分析：
1. 技术面分析：
[详细分析内容，包括所有技术指标的具体信号]

2. 新闻影响分析：
[详细分析内容，包括新闻情感分析结果]

3. 价格设置依据：
[解释入场价、止盈价和止损价的设置原因，必须包含具体的百分比计算]

4. 潜在风险提示：
[详细说明可能影响交易的风险因素]

注意：
1. 趋势判断必须明确，不要模棱两可
2. 所有价格必须是具体的数字，且严格符合上述百分比范围要求
3. 价格必须基于当前市场价格进行合理设置
4. 分析要客观专业，并提供具体理由
5. 严格按照上述格式输出，不要改变格式
"""

            # 调用DeepSeek AI进行分析
            headers = {
                'Authorization': f'Bearer {self.ai_api_key.text()}',
                'Content-Type': 'application/json'
            }
            
            api_url = "https://api.deepseek.com/v1/chat/completions"
            api_data = {
                "model": "deepseek-chat",
                "messages": [{"role": "user", "content": prompt}],
                "temperature": 0.7,
                "timeout": 120
            }
            
            # 使用session进行请求，增加重试机制
            session = requests.Session()
            retries = Retry(
                total=5,
                backoff_factor=1,
                status_forcelist=[500, 502, 503, 504]
            )
            session.mount('https://', HTTPAdapter(max_retries=retries))
            
            response = session.post(
                api_url, 
                json=api_data, 
                headers=headers, 
                timeout=(30, 120)  # (连接超时, 读取超时)
            )
            
            if response.status_code != 200:
                raise Exception(f"AI分析失败: {response.text}")
            
            analysis = response.json()['choices'][0]['message']['content']
            
            # 解析AI回复并更新界面
            self._update_analysis_ui(analysis)
            
        except Exception as e:
            error_msg = str(e)
            self.show_message_signal.emit("错误", f"分析过程出错: {error_msg}", "error")
            # 清空分析结果
            self.trend_value.setText("")
            self.entry_price_value.setText("")
            self.tp_price_value.setText("")
            self.sl_price_value.setText("")
            self.analysis_text.setText(f"分析失败: {error_msg}")
        finally:
            self.setEnabled(True)  # 启用界面

    def _update_analysis_ui(self, analysis):
        """更新分析结果界面"""
        try:
            # 解析AI回复中的趋势
            trend_match = re.search(r"市场趋势：\s*([看多|看空]+)", analysis)
            if trend_match:
                trend = trend_match.group(1)
                self.trend_value.setText(trend)
                self.trend_value.setStyleSheet("color: #2EBD85;" if trend == "看多" else "color: #F23645;")
            
            # 提取价格信息
            entry_match = re.search(r"入场价位：\s*(\d+\.?\d*)\s*USDT", analysis)
            if entry_match:
                self.entry_price_value.setText(entry_match.group(1))
            
            tp_match = re.search(r"止盈价位：\s*(\d+\.?\d*)\s*USDT", analysis)
            if tp_match:
                self.tp_price_value.setText(tp_match.group(1))
            
            sl_match = re.search(r"止损价位：\s*(\d+\.?\d*)\s*USDT", analysis)
            if sl_match:
                self.sl_price_value.setText(sl_match.group(1))
            
            # 提取详细分析部分
            technical_match = re.search(r"1\. 技术面分析：\n(.*?)\n\n2\.", analysis, re.DOTALL)
            news_match = re.search(r"2\. 新闻影响分析：\n(.*?)\n\n3\.", analysis, re.DOTALL)
            price_match = re.search(r"3\. 价格设置依据：\n(.*?)\n\n4\.", analysis, re.DOTALL)
            risk_match = re.search(r"4\. 潜在风险提示：\n(.*?)(?:\n\n|$)", analysis, re.DOTALL)
            
            # 增强格式化的分析文本
            formatted_analysis = ""
            
            # 添加标题和交易方向
            title_bg_color = "#2EBD85" if trend == "看多" else "#F23645"
            formatted_analysis += f'<div style="background-color: {title_bg_color}; color: white; padding: 10px; font-weight: bold; font-size: 16px; margin-bottom: 10px; border-radius: 5px;">'
            formatted_analysis += f"AI分析结果 - {trend.upper()}"
            formatted_analysis += "</div>"
            
            # 价格信息区域
            formatted_analysis += '<div style="background-color: #1E2329; padding: 10px; margin-bottom: 10px; border-radius: 5px;">'
            formatted_analysis += '<span style="color: #F0B90B; font-weight: bold;">价格信息</span><br>'
            if entry_match:
                formatted_analysis += f'入场价位: <span style="color: white; font-weight: bold;">{entry_match.group(1)} USDT</span><br>'
            if tp_match:
                formatted_analysis += f'止盈价位: <span style="color: #2EBD85; font-weight: bold;">{tp_match.group(1)} USDT</span><br>'
            if sl_match:
                formatted_analysis += f'止损价位: <span style="color: #F23645; font-weight: bold;">{sl_match.group(1)} USDT</span>'
            formatted_analysis += '</div>'
            
            # 技术面分析
            formatted_analysis += '<div style="background-color: #1E2329; padding: 10px; margin-bottom: 10px; border-radius: 5px;">'
            formatted_analysis += '<span style="color: #F0B90B; font-weight: bold;">技术面分析</span><br>'
            if technical_match:
                # 高亮重要关键词
                tech_text = technical_match.group(1).strip()
                tech_text = re.sub(r'(金叉|上穿|突破上轨|超买区|趋势强|黄金交叉)', r'<span style="color: #2EBD85; font-weight: bold;">\1</span>', tech_text)
                tech_text = re.sub(r'(死叉|下穿|突破下轨|超卖区|趋势弱|死亡交叉)', r'<span style="color: #F23645; font-weight: bold;">\1</span>', tech_text)
                tech_text = re.sub(r'(RSI|MACD|EMA|ADX|CCI|MFI|随机指标|布林带)', r'<span style="color: #F0B90B;">\1</span>', tech_text)
                formatted_analysis += tech_text
            formatted_analysis += '</div>'
            
            # 新闻影响分析
            formatted_analysis += '<div style="background-color: #1E2329; padding: 10px; margin-bottom: 10px; border-radius: 5px;">'
            formatted_analysis += '<span style="color: #F0B90B; font-weight: bold;">新闻影响分析</span><br>'
            if news_match:
                news_text = news_match.group(1).strip()
                # 高亮关键词
                news_text = re.sub(r'(利好|积极|上涨|增长|利润|看多|乐观)', r'<span style="color: #2EBD85; font-weight: bold;">\1</span>', news_text)
                news_text = re.sub(r'(利空|消极|下跌|减少|亏损|看空|悲观)', r'<span style="color: #F23645; font-weight: bold;">\1</span>', news_text)
                # 增加监管和机构动态相关关键词的高亮
                news_text = re.sub(r'(特朗普|美联储|SEC|CFTC|监管|政策|法案|法规|财政部|央行|耶伦|机构|黑石|ETF|摩根大通|高盛|传统金融|银行|合规|反洗钱|KYC)', 
                                  r'<span style="color: #F0B90B; font-weight: bold;">\1</span>', news_text)
                formatted_analysis += news_text
            formatted_analysis += '</div>'
            
            # 价格设置依据
            formatted_analysis += '<div style="background-color: #1E2329; padding: 10px; margin-bottom: 10px; border-radius: 5px;">'
            formatted_analysis += '<span style="color: #F0B90B; font-weight: bold;">价格设置依据</span><br>'
            if price_match:
                price_text = price_match.group(1).strip()
                # 高亮数字和百分比
                price_text = re.sub(r'(\d+\.?\d*%|\d+\.?\d*\s*USDT)', r'<span style="color: white; font-weight: bold;">\1</span>', price_text)
                formatted_analysis += price_text
            formatted_analysis += '</div>'
            
            # 潜在风险提示
            formatted_analysis += '<div style="background-color: #1E2329; padding: 10px; border-radius: 5px;">'
            formatted_analysis += '<span style="color: #F0B90B; font-weight: bold;">潜在风险提示</span><br>'
            if risk_match:
                risk_text = risk_match.group(1).strip()
                # 高亮警告词汇
                risk_text = re.sub(r'(风险|波动|不确定性|谨慎|注意|警惕|可能|反转)', r'<span style="color: #F23645; font-weight: bold;">\1</span>', risk_text)
                formatted_analysis += risk_text
            formatted_analysis += '</div>'
            
            # 更新分析文本
            self.analysis_text.setHtml(formatted_analysis)
            
            # 检查是否成功提取了所有价格
            if not self.trend_value.text():
                self.log_trading("未能提取到市场趋势", level='warning')
            if not self.entry_price_value.text():
                self.log_trading("未能提取到入场价位", level='warning')
            if not self.tp_price_value.text():
                self.log_trading("未能提取到止盈价位", level='warning')
            if not self.sl_price_value.text():
                self.log_trading("未能提取到止损价位", level='warning')
            
            # 记录分析结果到日志，使用HTML格式增强可读性
            trend_color = "#2EBD85" if trend == "看多" else "#F23645"
            log_message = (
                f"<div style='margin: 5px 0;'>"
                f"<span style='font-weight: bold; color: #F0B90B;'>分析结果</span><br>"
                f"市场趋势: <span style='color: {trend_color}; font-weight: bold;'>{self.trend_value.text()}</span><br>"
                f"入场价位: <span style='color: white;'>{self.entry_price_value.text()} USDT</span><br>"
                f"止盈价位: <span style='color: #2EBD85;'>{self.tp_price_value.text()} USDT</span><br>"
                f"止损价位: <span style='color: #F23645;'>{self.sl_price_value.text()} USDT</span>"
                f"</div>"
            )
            self.update_log_signal.emit(log_message)
            
        except Exception as e:
            self.log_trading(f"更新分析界面错误: {str(e)}", level='error')
            # 打印完整的分析文本以便调试
            self.log_trading("完整分析文本:", level='debug')
            self.log_trading(analysis, level='debug')

    def test_news_api(self):
        """测试News API是否有效"""
        try:
            api_key = self.news_api_key.text()
            
            if not api_key:
                self.show_message_signal.emit("警告", "请先输入News API密钥！", "warning")
                return
            
            # 测试API
            news_url = 'https://newsapi.org/v2/everything'
            news_params = {
                'q': 'cryptocurrency',
                'pageSize': 1,  # 只获取一条新闻进行测试
                'language': 'en',
                'apiKey': api_key
            }
            
            response = requests.get(news_url, params=news_params)
            data = response.json()
            
            if response.status_code == 200 and data.get('status') == 'ok':
                self.show_message_signal.emit("成功", "News API连接测试成功！", "info")
            else:
                error_message = data.get('message', '未知错误')
                raise Exception(error_message)
                
        except Exception as e:
            self.show_message_signal.emit("错误", f"News API测试失败: {str(e)}", "error")
            
    def test_ai_api(self):
        """测试DeepSeek AI API是否有效"""
        try:
            api_key = self.ai_api_key.text()
            
            if not api_key:
                self.show_message_signal.emit("警告", "请先输入DeepSeek AI API密钥！", "warning")
                return
            
            # 测试API
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Content-Type': 'application/json'
            }
            
            api_url = "https://api.deepseek.com/v1/chat/completions"
            api_data = {
                "model": "deepseek-chat",
                "messages": [{"role": "user", "content": "Hello"}],
                "temperature": 0.7
            }
            
            response = requests.post(api_url, json=api_data, headers=headers)
            
            if response.status_code == 200:
                self.show_message_signal.emit("成功", "DeepSeek AI API连接测试成功！", "info")
            else:
                error_message = response.json().get('error', {}).get('message', '未知错误')
                raise Exception(error_message)
                
        except Exception as e:
            self.show_message_signal.emit("错误", f"DeepSeek AI API测试失败: {str(e)}", "error")

    def toggle_auto_trading(self):
        """切换自动交易状态"""
        try:
            self.auto_trading_enabled = not self.auto_trading_enabled
            
            if self.auto_trading_enabled:
                # 检查API配置
                if not self.news_api_key.text() or not self.ai_api_key.text():
                    self.show_message_signal.emit("警告", "请先配置News API和DeepSeek AI API！", "warning")
                    self.auto_trading_enabled = False
                    return
                
                # 检查交易参数
                if self.trade_amount_input.value() <= 0:
                    self.show_message_signal.emit("警告", "请设置有效的交易金额！", "warning")
                    self.auto_trading_enabled = False
                    return
                
                # 更新按钮状态
                self.auto_trading_button.setText("停止自动交易")
                self.auto_trading_button.setStyleSheet("background-color: #F23645;")
                
                # 禁用设置选项
                self.ai_trading_symbol_combo.setEnabled(False)
                self.trade_amount_input.setEnabled(False)
                self.ai_leverage_spinbox.setEnabled(False)
                self.market_order_radio.setEnabled(False)
                self.limit_order_radio.setEnabled(False)
                self.use_ai_price_radio.setEnabled(False)
                self.use_current_price_radio.setEnabled(False)
                
                # 启动自动交易
                self.start_auto_trading()
                
            else:
                # 更新按钮状态
                self.auto_trading_button.setText("启动自动交易")
                self.auto_trading_button.setStyleSheet("")
                
                # 启用设置选项
                self.ai_trading_symbol_combo.setEnabled(True)
                self.trade_amount_input.setEnabled(True)
                self.ai_leverage_spinbox.setEnabled(True)
                self.market_order_radio.setEnabled(True)
                self.limit_order_radio.setEnabled(True)
                self.use_ai_price_radio.setEnabled(True)
                self.use_current_price_radio.setEnabled(True)
                
        except Exception as e:
            self.show_message_signal.emit("错误", f"切换自动交易状态失败: {str(e)}", "error")
            self.auto_trading_enabled = False
            self.auto_trading_button.setText("启动自动交易")
            self.auto_trading_button.setStyleSheet("")
            
    def start_auto_trading(self):
        """启动自动交易"""
        if not hasattr(self, 'auto_trading_thread') or not self.auto_trading_thread.is_alive():
            self.auto_trading_enabled = True
            self.auto_trading_thread = threading.Thread(target=self.auto_trading_loop, daemon=True)
            self.auto_trading_thread.start()
        else:
            self.log_trading("自动交易已在运行中")

    def auto_trading_loop(self):
        """自动交易循环"""
        error_count = 0
        max_errors = 5
        self.last_trigger_price = None
        initial_analysis = True

        while self.auto_trading_enabled:
            try:
                with self.trade_lock:
                    symbol = self.ai_trading_symbol_combo.currentText()
                    base_symbol = symbol.split('/')[0]
                    
                    ticker = self.exchange.fetch_ticker(symbol)
                    current_price = round(ticker['last'], 2)
                    
                    # 获取触发分析阈值
                    trigger_threshold = self.trigger_threshold_spinbox.value()
                    
                    # 触发条件判断
                    trigger_analysis = False
                    if initial_analysis:
                        trigger_analysis = True
                        initial_analysis = False
                        self.log_trading("执行首次分析", level='info')
                    elif self.last_trigger_price is not None:
                        price_change = abs((current_price - self.last_trigger_price) / self.last_trigger_price * 100)
                        price_change = round(price_change, 2)
                        
                        if price_change >= trigger_threshold:
                            trigger_analysis = True
                            self.log_trading(f"价格波动达到{price_change:.2f}%，触发分析", level='info')

                    if trigger_analysis:
                        self.last_trigger_price = current_price
                        # 获取并分析新闻
                        self.log_trading(f"正在分析{base_symbol}相关新闻...")
                        analysis_result = self.get_trading_signal(base_symbol)
                        
                        if analysis_result:
                            trend = analysis_result.get('trend')
                            entry_price = float(analysis_result.get('entry_price', 0))
                            tp_price = float(analysis_result.get('tp_price', 0))
                            sl_price = float(analysis_result.get('sl_price', 0))
                            market_state = analysis_result.get('market_state', 'RANGING_WEAK_TREND')
                            trend_strength = analysis_result.get('trend_strength', 0)
                            
                            # 记录市场状态和趋势强度
                            self.log_trading(f"市场状态: {market_state}, 趋势强度: {trend_strength:.2f}")
                            
                            if trend and entry_price and tp_price and sl_price:
                                # 获取交易对的最小交易数量限制
                                markets = self.exchange.fetch_markets()
                                market = next((m for m in markets if m['symbol'] == symbol), None)
                                if not market:
                                    self.log_trading(f"未找到{symbol}的市场信息")
                                    continue
                                    
                                min_amount = float(market['limits']['amount']['min'])
                                self.log_trading(f"最小交易数量: {min_amount} {base_symbol}")
                                
                                # 直接使用输入的合约数量，但根据市场状态可能调整
                                base_quantity = self.trade_amount_input.value()
                                
                                # 根据市场状态调整交易策略
                                if market_state == "STRONG_UPTREND":
                                    # 在强上涨趋势中
                                    if trend == "看多":
                                        # 顺势交易，可以适当增加仓位
                                        quantity_multiplier = 1.2  # 增加20%仓位
                                        self.log_trading("强上涨趋势中的看多信号，增加20%仓位")
                                    else:  # trend == "看空"
                                        # 逆势交易，需要更谨慎，减少仓位
                                        quantity_multiplier = 0.7  # 减少30%仓位
                                        self.log_trading("强上涨趋势中的看空信号，减少30%仓位")
                                elif market_state == "STRONG_DOWNTREND":
                                    # 在强下跌趋势中
                                    if trend == "看空":
                                        # 顺势交易，可以适当增加仓位
                                        quantity_multiplier = 1.2  # 增加20%仓位
                                        self.log_trading("强下跌趋势中的看空信号，增加20%仓位")
                                    else:  # trend == "看多"
                                        # 逆势交易，需要更谨慎，减少仓位
                                        quantity_multiplier = 0.7  # 减少30%仓位
                                        self.log_trading("强下跌趋势中的看多信号，减少30%仓位")
                                else:  # "RANGING_WEAK_TREND"
                                    # 震荡市场，保持正常仓位
                                    quantity_multiplier = 1.0
                                    self.log_trading("震荡市场，使用标准仓位")
                                
                                # 应用乘数调整仓位
                                quantity = base_quantity * quantity_multiplier
                                
                                # 确保数量至少为最小交易量，并向上取整到0.01
                                quantity = max(min_amount, round(quantity / min_amount) * min_amount)
                                self.log_trading(f"调整后的交易数量: {quantity} {base_symbol}")
                                
                                # 当价格接近目标价格时，再次确认分析
                                price_diff = abs(current_price - entry_price)
                                if price_diff / entry_price < 0.02:  # 2%以内触发确认分析
                                    self.log_trading(f"价格接近目标价格，当前价:{current_price}，正在重新确认分析...")
                                    
                                    # 使用当前价格重新计算止盈止损价格
                                    if trend == '看多':
                                        new_entry_price = current_price
                                        new_tp_price = current_price * 1.007  # 0.7%止盈
                                        new_sl_price = current_price * 0.965  # 3.5%止损
                                    else:  # trend == '看空'
                                        new_entry_price = current_price
                                        new_tp_price = current_price * 0.993  # 0.7%止盈
                                        new_sl_price = current_price * 1.035  # 3.5%止损
                                    
                                    # 进行第二次分析确认趋势
                                    confirm_result = self.get_trading_signal(base_symbol)
                                    
                                    if confirm_result:
                                        confirm_trend = confirm_result.get('trend')
                                        
                                        # 添加趋势判断日志
                                        self.log_trading(
                                            f"分析比较:\n"
                                            f"初次分析 - 趋势: {trend}, 入场价: {entry_price}\n"
                                            f"确认分析 - 趋势: {confirm_trend}, 入场价: {new_entry_price}"
                                        )
                                        
                                        # 如果两次分析趋势一致，使用当前价格执行交易
                                        if confirm_trend == trend:
                                            try:
                                                # 确定交易方向
                                                side = 'buy' if trend == '看多' else 'sell'
                                                
                                                # 在强趋势市场中，即使确认分析的趋势不完全一致，也可能执行交易
                                                execute_trade = True
                                                
                                                # 根据市场状态调整交易确认逻辑
                                                self.log_trading(f"交易确认 - 市场状态: {market_state}, 初始趋势: {trend}, 确认趋势: {confirm_trend}")
                                                
                                                # 根据用户选择确定入场价格
                                                entry_price_to_use = None
                                                tp_price_to_use = None
                                                sl_price_to_use = None
                                                
                                                if self.use_ai_price_radio.isChecked():
                                                    # 使用AI分析的入场价格
                                                    entry_price_to_use = entry_price
                                                    tp_price_to_use = tp_price
                                                    sl_price_to_use = sl_price
                                                    
                                                    self.log_trading(f"使用AI分析入场价格: {entry_price_to_use} (原始AI分析价格)")
                                                else:
                                                    # 使用当前市价作为入场价格
                                                    ticker = self.exchange.fetch_ticker(symbol)
                                                    current_market_price = ticker['last']
                                                    entry_price_to_use = current_market_price
                                                    
                                                    # 根据市场状态调整止盈止损比例
                                                    tp_percent = self.tp_percent
                                                    sl_percent = self.sl_percent
                                                    
                                                    # 在强趋势中，可以调整止盈止损设置
                                                    if market_state == "STRONG_UPTREND" and side == 'buy':
                                                        tp_percent = tp_percent * 1.5
                                                        sl_percent = sl_percent * 0.8
                                                    elif market_state == "STRONG_DOWNTREND" and side == 'sell':
                                                        tp_percent = tp_percent * 1.5
                                                        sl_percent = sl_percent * 0.8
                                                    elif market_state == "RANGING_WEAK_TREND":
                                                        tp_percent = tp_percent * 0.8
                                                        sl_percent = sl_percent * 0.8
                                                    
                                                    # 重新计算止盈止损价格
                                                    if side == 'buy':
                                                        tp_price_to_use = entry_price_to_use * (1 + tp_percent / 100)
                                                        sl_price_to_use = entry_price_to_use * (1 - sl_percent / 100)
                                                    else:  # side == 'sell'
                                                        tp_price_to_use = entry_price_to_use * (1 - tp_percent / 100)
                                                        sl_price_to_use = entry_price_to_use * (1 + sl_percent / 100)
                                                    
                                                    self.log_trading(
                                                        f"使用当前市场价格: {entry_price_to_use}\n"
                                                        f"调整后止盈价格: {tp_price_to_use} ({tp_percent:.2f}%)\n"
                                                        f"调整后止损价格: {sl_price_to_use} ({sl_percent:.2f}%)"
                                                    )
                                                
                                                # 下单
                                                self.log_trading(
                                                    f"分析结果一致，准备{side}单:\n"
                                                    f"入场价: {entry_price_to_use:.2f}\n"
                                                    f"止盈价: {tp_price_to_use:.2f}\n"
                                                    f"止损价: {sl_price_to_use:.2f}"
                                                )
                                                
                                                order = self.place_ai_order(
                                                    symbol=symbol,
                                                    side=side,
                                                    amount=quantity,
                                                    entry_price=entry_price_to_use,
                                                    tp_price=tp_price_to_use,
                                                    sl_price=sl_price_to_use
                                                )
                                                
                                                if order:
                                                    self.log_trading(f"下单成功: {order['id']}")
                                                    # 监控订单状态
                                                    self.monitor_order_status(order['id'], symbol)
                                                
                                            except Exception as e:
                                                self.log_trading(f"下单失败: {str(e)}")
                                        else:
                                            # 两次分析趋势不一致，但在强趋势市场中，可能仍然执行交易
                                            execute_trade = False
                                            
                                            # 在强趋势中，如果初始分析与趋势方向一致，可以考虑执行交易
                                            if (market_state == "STRONG_UPTREND" and trend == "看多") or \
                                               (market_state == "STRONG_DOWNTREND" and trend == "看空"):
                                                # 强趋势中的顺势交易，即使确认分析不一致，也可能执行
                                                trend_strength = analysis_result.get('trend_strength', 0)
                                                if abs(trend_strength) > 4:  # 趋势强度非常高
                                                    execute_trade = True
                                                    self.log_trading(
                                                        f"虽然两次分析趋势不一致，但在强{trend}趋势中且趋势强度高({trend_strength:.2f})，"
                                                        f"决定继续执行{trend}单"
                                                    )
                                            
                                            if not execute_trade:
                                                self.log_trading(
                                                    "两次分析趋势不一致，可能存在市场波动，"
                                                    f"初次分析为{trend}，确认分析为{confirm_trend}，"
                                                    "为避免风险取消本次交易"
                                                )
                                            else:
                                                # 执行交易逻辑（与上面相同）
                                                try:
                                                    # 确定交易方向
                                                    side = 'buy' if trend == '看多' else 'sell'
                                                    
                                                    # 根据用户选择确定入场价格
                                                    entry_price_to_use = None
                                                    tp_price_to_use = None
                                                    sl_price_to_use = None
                                                    
                                                    if self.use_ai_price_radio.isChecked():
                                                        # 使用AI分析的入场价格
                                                        entry_price_to_use = entry_price
                                                        tp_price_to_use = tp_price
                                                        sl_price_to_use = sl_price
                                                        
                                                        self.log_trading(f"使用AI分析入场价格: {entry_price_to_use} (原始AI分析价格)")
                                                    else:
                                                        # 使用当前市价作为入场价格
                                                        ticker = self.exchange.fetch_ticker(symbol)
                                                        current_market_price = ticker['last']
                                                        entry_price_to_use = current_market_price
                                                        
                                                        # 根据市场状态调整止盈止损比例
                                                        tp_percent = self.tp_percent
                                                        sl_percent = self.sl_percent
                                                        
                                                        # 在强趋势中，可以调整止盈止损设置
                                                        if market_state == "STRONG_UPTREND" and side == 'buy':
                                                            tp_percent = tp_percent * 1.5
                                                            sl_percent = sl_percent * 0.8
                                                        elif market_state == "STRONG_DOWNTREND" and side == 'sell':
                                                            tp_percent = tp_percent * 1.5
                                                            sl_percent = sl_percent * 0.8
                                                        elif market_state == "RANGING_WEAK_TREND":
                                                            tp_percent = tp_percent * 0.8
                                                            sl_percent = sl_percent * 0.8
                                                        
                                                        # 重新计算止盈止损价格
                                                        if side == 'buy':
                                                            tp_price_to_use = entry_price_to_use * (1 + tp_percent / 100)
                                                            sl_price_to_use = entry_price_to_use * (1 - sl_percent / 100)
                                                        else:  # side == 'sell'
                                                            tp_price_to_use = entry_price_to_use * (1 - tp_percent / 100)
                                                            sl_price_to_use = entry_price_to_use * (1 + sl_percent / 100)
                                                    
                                                    # 下单
                                                    self.log_trading(
                                                        f"强趋势中执行{side}单:\n"
                                                        f"入场价: {entry_price_to_use:.2f}\n"
                                                        f"止盈价: {tp_price_to_use:.2f}\n"
                                                        f"止损价: {sl_price_to_use:.2f}"
                                                    )
                                                    
                                                    order = self.place_ai_order(
                                                        symbol=symbol,
                                                        side=side,
                                                        amount=quantity,
                                                        entry_price=entry_price_to_use,
                                                        tp_price=tp_price_to_use,
                                                        sl_price=sl_price_to_use
                                                    )
                                                    
                                                    if order:
                                                        self.log_trading(f"下单成功: {order['id']}")
                                                        # 监控订单状态
                                                        self.monitor_order_status(order['id'], symbol)
                                                    
                                                except Exception as e:
                                                    self.log_trading(f"下单失败: {str(e)}")
                        else:
                            self.log_trading("无法获取交易信号，取消本次交易")
                
                # 缩短检查间隔
                time.sleep(5)
            
            except Exception as e:
                error_count += 1
                self.log_trading(f"自动交易循环错误 ({error_count}/{max_errors}): {str(e)}", level='error')
                
                if error_count >= max_errors:
                    self.log_trading("错误次数过多，自动停止交易", level='error')
                    self.auto_trading_enabled = False
                    self.show_message_signal.emit("错误", "自动交易因多次错误已停止", "error")
                    break
            
            else:
                error_count = 0
            
            finally:
                gc.collect()
                # 添加内存监控
                if gc.get_count()[0] > 1000:  # 如果待回收对象过多
                    gc.collect()  # 强制垃圾回收

    def get_trading_signal(self, symbol):
        """获取交易信号"""
        try:
            max_retries = 5
            retry_delay = 10
            timeout = 120  # 增加超时时间到120秒
            
            # 在函数开始就初始化可能在f-string中使用的变量，确保它们在所有代码路径中都有值
            market_state = "RANGING_WEAK_TREND"  # 默认为震荡或弱趋势
            adx_value = 0.0
            plus_di_value = 0.0
            minus_di_value = 0.0
            current_adx_threshold = 25
            trend_strength = 0.0
            trend_signals = []
            avg_news_sentiment = 0.0
            
            for attempt in range(max_retries):
                try:
                    # 修改获取市场数据部分，使用正确的交易对格式
                    swap_symbol = f"{symbol}-USDT-SWAP"  # 使用永续合约格式
                    ticker = self.exchange.fetch_ticker(swap_symbol)
                    current_price = ticker['last']
                    
                    # 获取K线数据也使用永续合约格式
                    ohlcv = self.exchange.fetch_ohlcv(swap_symbol, '15m', limit=100)
                    df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                    
                    # 添加价格精度处理
                    current_price = round(current_price, 2)
                    volatility = round(abs((current_price - self.last_price) / self.last_price * 100) if hasattr(self, 'last_price') else 0, 2)
                    self.last_price = current_price
                    
                    # 获取新闻数据，增加更多新闻源和关键词
                    news_sources = [
                        {
                            'url': 'https://newsapi.org/v2/everything',
                            'params': {
                                'q': f'(Trump OR Federal Reserve OR SEC OR regulation OR "crypto regulation" OR "CFTC" OR "Treasury Department" OR "central bank" OR "Yellen" OR "institutional adoption" OR "Blackrock" OR "JP Morgan" OR "Goldman Sachs" OR "digital currency") AND ({symbol} OR cryptocurrency OR "digital asset" OR blockchain)',
                                'sortBy': 'publishedAt',
                                'language': 'en',
                                'pageSize': 6,
                                'apiKey': self.news_api_key.text()
                            }
                        },
                        {
                            'url': 'https://newsapi.org/v2/everything',
                            'params': {
                                'q': f'(market analysis OR price prediction OR volatility OR "market sentiment" OR "trading volume" OR "bullish sentiment" OR "bearish outlook") AND ({symbol} OR cryptocurrency)',
                                'sortBy': 'publishedAt',
                                'language': 'en',
                                'pageSize': 6,
                                'apiKey': self.news_api_key.text()
                            }
                        }
                    ]
                    
                    all_articles = []
                    for source in news_sources:
                        try:
                            news_response = requests.get(source['url'], params=source['params'], timeout=10)
                            if news_response.status_code == 200:
                                news_data = news_response.json()
                                articles = news_data.get('articles', [])
                                all_articles.extend(articles)
                        except Exception as e:
                            self.log_trading(f"获取新闻源失败: {str(e)}")
                    
                    if not all_articles:
                        self.log_trading("未找到相关新闻")
                        return None
                    
                    # 新闻情感分析
                    news_sentiment = 0
                    news_summary = "最新加密货币相关新闻摘要：\n\n"
                    
                    for article in all_articles[:10]:  # 分析前10条新闻
                        title = article['title']
                        description = article.get('description', '')
                        content = f"{title} {description}"
                        
                        # 情感分析关键词
                        positive_keywords = ['bullish', 'surge', 'rise', 'gain', 'positive', 'growth', 'adoption', 'approval', 'institutional investment', 'breakthrough', 'rally', 'endorsement', 'partnership', 'launch', 'innovation']
                        negative_keywords = ['bearish', 'crash', 'drop', 'fall', 'negative', 'decline', 'ban', 'reject', 'regulation concerns', 'investigation', 'lawsuit', 'hack', 'security breach', 'restrictions', 'vulnerability', 'criticism']
                        
                        # 计算情感分数，引入关键词权重
                        sentiment_score = 0
                        
                        # 高权重关键词
                        high_impact_positive = ['institutional adoption', 'ETF approval', 'major partnership', 'regulatory clarity', 'mainstream adoption']
                        high_impact_negative = ['trading ban', 'major hack', 'SEC lawsuit', 'criminal charges', 'severe restriction']
                        
                        # 检查高权重关键词
                        for keyword in high_impact_positive:
                            if keyword.lower() in content.lower():
                                sentiment_score += 2  # 高权重积极关键词计2分
                        
                        for keyword in high_impact_negative:
                            if keyword.lower() in content.lower():
                                sentiment_score -= 2  # 高权重消极关键词计-2分
                        
                        # 检查普通关键词
                        for keyword in positive_keywords:
                            if keyword.lower() in content.lower():
                                sentiment_score += 1
                                
                        for keyword in negative_keywords:
                            if keyword.lower() in content.lower():
                                sentiment_score -= 1
                        
                        news_sentiment += sentiment_score
                        
                        # 添加到新闻摘要
                        news_summary += f"- {title}\n"
                        if description:
                            news_summary += f"  摘要: {description[:200]}...\n"
                        news_summary += f"  情感倾向: {'看多' if sentiment_score > 0 else '看空' if sentiment_score < 0 else '中性'}\n\n"
                    
                    # 计算整体新闻情感，增加权重影响
                    avg_news_sentiment = news_sentiment / len(all_articles[:10])
                    # 降低中性判断阈值，使系统更容易得出明确的多空信号
                    news_trend = "看多" if avg_news_sentiment > 0.4 else "看空" if avg_news_sentiment < -0.4 else "中性"
                    
                    # 初始化趋势强度和信号列表
                    trend_strength = 0
                    trend_signals = []
                    
                    # 将新闻情感纳入趋势强度计算，增加权重
                    if news_trend == "看多":
                        trend_strength += 2  # 将权重从1增加到2
                        trend_signals.append("新闻情感明显偏多")
                    elif news_trend == "看空":
                        trend_strength -= 2  # 将权重从1增加到2
                        trend_signals.append("新闻情感明显偏空")
                    
                    # 计算更多技术指标
                    close_prices = df['close'].values
                    high_prices = df['high'].values
                    low_prices = df['low'].values
                    volume = df['volume'].values
                    
                    # 基础指标 - 使用用户设置的参数
                    rsi = talib.RSI(close_prices, timeperiod=getattr(self, 'rsi_period', 14))
                    macd, signal, hist = talib.MACD(
                        close_prices, 
                        fastperiod=getattr(self, 'macd_fast', 12), 
                        slowperiod=getattr(self, 'macd_slow', 26), 
                        signalperiod=getattr(self, 'macd_signal', 9)
                    )
                    upper, middle, lower = talib.BBANDS(
                        close_prices, 
                        timeperiod=getattr(self, 'bb_period', 20),
                        nbdevup=getattr(self, 'bb_std', 2.0),
                        nbdevdn=getattr(self, 'bb_std', 2.0),
                        matype=0
                    )
                    
                    # 趋势指标 - 使用用户设置的参数
                    ema20 = talib.EMA(close_prices, timeperiod=getattr(self, 'ema_short', 20))
                    ema50 = talib.EMA(close_prices, timeperiod=getattr(self, 'ema_medium', 50))
                    ema200 = talib.EMA(close_prices, timeperiod=getattr(self, 'ema_long', 200))
                    adx = talib.ADX(high_prices, low_prices, close_prices, timeperiod=getattr(self, 'adx_period', 14))
                    di_plus = talib.PLUS_DI(high_prices, low_prices, close_prices, timeperiod=getattr(self, 'adx_period', 14))
                    di_minus = talib.MINUS_DI(high_prices, low_prices, close_prices, timeperiod=getattr(self, 'adx_period', 14))
                    
                    # 动量指标
                    stoch_k, stoch_d = talib.STOCH(high_prices, low_prices, close_prices)
                    cci = talib.CCI(high_prices, low_prices, close_prices)
                    mfi = talib.MFI(high_prices, low_prices, close_prices, volume)
                    
                    # 波动指标
                    atr = talib.ATR(high_prices, low_prices, close_prices)
                    natr = talib.NATR(high_prices, low_prices, close_prices)
                    
                    # 成交量指标
                    obv = talib.OBV(close_prices, volume)
                    ad = talib.AD(high_prices, low_prices, close_prices, volume)
                    
                    # --- 市场状态判断 ---
                    market_state = "RANGING_WEAK_TREND" # 默认为震荡或弱趋势
                    adx_value = adx[-1]
                    plus_di_value = di_plus[-1]
                    minus_di_value = di_minus[-1]
                    current_adx_threshold = getattr(self, 'adx_threshold', 25)

                    if adx_value > current_adx_threshold:
                        if plus_di_value > minus_di_value:
                            market_state = "STRONG_UPTREND"
                        elif minus_di_value > plus_di_value:
                            market_state = "STRONG_DOWNTREND"
                        # else: ADX强但方向不明，仍可视为RANGING_WEAK_TREND或特定处理
                    
                    self.log_trading(f"市场状态判断: {market_state} (ADX: {adx_value:.2f}, +DI: {plus_di_value:.2f}, -DI: {minus_di_value:.2f}, 阈值: {current_adx_threshold})")

                    # RSI信号 - 使用用户设置的阈值
                    rsi_value = rsi[-1]
                    rsi_overbought = getattr(self, 'rsi_overbought', 70)
                    rsi_oversold = getattr(self, 'rsi_oversold', 30)
                    if market_state == "STRONG_UPTREND":
                        if rsi_value > rsi_overbought: # 超买在强上涨趋势中可能意味着趋势持续
                            trend_strength += 0.5 # 轻微加分
                            trend_signals.append(f"RSI超买({rsi_value:.2f})，强上涨趋势中，可能持续")
                        elif rsi_value < rsi_oversold: # 超卖在强上涨趋势中是强烈的反向信号，需谨慎，暂时不减分或轻微减分
                            trend_strength -= 0.25
                            trend_signals.append(f"RSI超卖({rsi_value:.2f})，与强上涨趋势矛盾，警惕")
                    elif market_state == "STRONG_DOWNTREND":
                        if rsi_value < rsi_oversold: # 超卖在强下跌趋势中可能意味着趋势持续
                            trend_strength -= 0.5 # 轻微加分（负向）
                            trend_signals.append(f"RSI超卖({rsi_value:.2f})，强下跌趋势中，可能持续")
                        elif rsi_value > rsi_overbought: # 超买在强下跌趋势中是强烈的反向信号，需谨慎
                            trend_strength += 0.25
                            trend_signals.append(f"RSI超买({rsi_value:.2f})，与强下跌趋势矛盾，警惕")
                    else: # RANGING_WEAK_TREND
                        if rsi_value > rsi_overbought:
                            trend_strength -= 1
                            trend_signals.append(f"RSI超买 ({rsi_value:.2f} > {rsi_overbought})，震荡市看空信号")
                        elif rsi_value < rsi_oversold:
                            trend_strength += 1
                            trend_signals.append(f"RSI超卖 ({rsi_value:.2f} < {rsi_oversold})，震荡市看多信号")
                    
                    # MACD信号
                    macd_value = macd[-1]
                    signal_value = signal[-1]
                    prev_macd_value = macd[-2]
                    prev_signal_value = signal[-2]
                    if macd_value > signal_value and prev_macd_value <= prev_signal_value: # 金叉
                        if market_state == "STRONG_UPTREND":
                            trend_strength += 2 # 强趋势下，顺势信号权重增加
                            trend_signals.append("MACD金叉，强上涨趋势中，强烈看多")
                        elif market_state == "STRONG_DOWNTREND":
                            trend_strength += 0.25 # 逆势信号，小幅加分或忽略
                            trend_signals.append("MACD金叉，与强下跌趋势矛盾，谨慎")
                        else: # RANGING_WEAK_TREND
                            trend_strength += 1
                            trend_signals.append("MACD金叉，震荡市看多信号")
                    elif macd_value < signal_value and prev_macd_value >= prev_signal_value: # 死叉
                        if market_state == "STRONG_DOWNTREND":
                            trend_strength -= 2 # 强趋势下，顺势信号权重增加
                            trend_signals.append("MACD死叉，强下跌趋势中，强烈看空")
                        elif market_state == "STRONG_UPTREND":
                            trend_strength -= 0.25 # 逆势信号，小幅减分或忽略
                            trend_signals.append("MACD死叉，与强上涨趋势矛盾，谨慎")
                        else: # RANGING_WEAK_TREND
                            trend_strength -= 1
                            trend_signals.append("MACD死叉，震荡市看空信号")
                    
                    # 布林带信号
                    upper_band = upper[-1]
                    lower_band = lower[-1]
                    if market_state == "STRONG_UPTREND":
                        if current_price > upper_band: # 突破上轨，趋势持续
                            trend_strength += 1.5
                            trend_signals.append("价格突破布林上轨，强上涨趋势持续")
                        elif current_price < lower_band: # 跌破下轨，趋势反转信号，谨慎
                            trend_strength -= 0.5 
                            trend_signals.append("价格跌破布林下轨，与强上涨趋势矛盾，警惕")
                    elif market_state == "STRONG_DOWNTREND":
                        if current_price < lower_band: # 突破下轨，趋势持续
                            trend_strength -= 1.5
                            trend_signals.append("价格突破布林下轨，强下跌趋势持续")
                        elif current_price > upper_band: # 突破上轨，趋势反转信号，谨慎
                            trend_strength += 0.5
                            trend_signals.append("价格突破布林上轨，与强下跌趋势矛盾，警惕")
                    else: # RANGING_WEAK_TREND
                        if current_price > upper_band:
                            trend_strength -= 1
                            trend_signals.append("价格突破布林上轨，震荡市看空")
                        elif current_price < lower_band:
                            trend_strength += 1
                            trend_signals.append("价格突破布林下轨，震荡市看多")
                    
                    # EMA信号
                    ema20_value = ema20[-1]
                    ema50_value = ema50[-1]
                    prev_ema20_value = ema20[-2]
                    prev_ema50_value = ema50[-2]
                    if ema20_value > ema50_value and prev_ema20_value <= prev_ema50_value: # 短期上穿中期
                        if market_state == "STRONG_UPTREND":
                            trend_strength += 1.5
                            trend_signals.append("EMA短穿中(金叉)，强上涨趋势中，看多")
                        else:
                            trend_strength += 0.5 # 在其他市场状态下，权重较低
                            trend_signals.append("EMA短穿中(金叉)，看多信号")
                    elif ema20_value < ema50_value and prev_ema20_value >= prev_ema50_value: # 短期下穿中期
                        if market_state == "STRONG_DOWNTREND":
                            trend_strength -= 1.5
                            trend_signals.append("EMA短穿中(死叉)，强下跌趋势中，看空")
                        else:
                            trend_strength -= 0.5 # 在其他市场状态下，权重较低
                            trend_signals.append("EMA短穿中(死叉)，看空信号")
                    
                    # ADX趋势强度信号 (已在市场状态判断时使用，这里可以再次确认或细化)
                    # 在强趋势市场，ADX本身已经贡献了市场状态的判断，其对trend_strength的直接影响已在前面体现
                    # 此处可以根据ADX的绝对值大小，对强趋势的强度做进一步细化，例如ADX > 40 则更强
                    if market_state == "STRONG_UPTREND" and adx_value > getattr(self, 'adx_strong_threshold', 40):
                        trend_strength += 0.5 # 额外加分给极强趋势
                        trend_signals.append(f"ADX极强上涨 ({adx_value:.2f})")
                    elif market_state == "STRONG_DOWNTREND" and adx_value > getattr(self, 'adx_strong_threshold', 40):
                        trend_strength -= 0.5 # 额外减分给极强趋势
                        trend_signals.append(f"ADX极强下跌 ({adx_value:.2f})")
                    # 对于之前的ADX信号逻辑，现在已经整合到market_state的判断里了
                    # 原有的ADX对trend_strength的直接加减分逻辑可以移除或重构，避免重复计算影响
                    # 这里保留了原始的 `elif di_minus[-1] > di_plus[-1]` 等判断，因为它们在 `market_state` 中已经使用。
                    # 下面的代码块是基于旧的直接修改trend_strength，现在应该由market_state指导
                    # Ensure this part is reviewed and potentially removed or refactored:
                    # current_adx_threshold = getattr(self, 'adx_threshold', 25)
                    # if adx_value > current_adx_threshold:
                    #     if plus_di_value > minus_di_value:
                    #         trend_strength += 1.5 
                    #         trend_signals.append(f"ADX显示强势上涨趋势 (ADX: {adx_value:.2f} > {current_adx_threshold}, +DI: {plus_di_value:.2f}, -DI: {minus_di_value:.2f})")
                    #     elif minus_di_value > plus_di_value:
                    #         trend_strength -= 1.5 
                    #         trend_signals.append(f"ADX显示强势下跌趋势 (ADX: {adx_value:.2f} > {current_adx_threshold}, +DI: {plus_di_value:.2f}, -DI: {minus_di_value:.2f})")
                    #     else:
                    #         trend_signals.append(f"ADX趋势强但方向不明 (ADX: {adx_value:.2f} > {current_adx_threshold}, +DI: {plus_di_value:.2f}, -DI: {minus_di_value:.2f})")
                    # else:
                    #     trend_signals.append(f"ADX显示无明显趋势 (ADX: {adx_value:.2f} <= {current_adx_threshold})")

                    # 随机指标 (Stochastic) 信号 - 在震荡市中权重较高
                    stoch_k_value = stoch_k[-1]
                    stoch_d_value = stoch_d[-1]
                    stoch_overbought = getattr(self, 'stoch_overbought', 80)
                    stoch_oversold = getattr(self, 'stoch_oversold', 20)
                    if market_state == "RANGING_WEAK_TREND":
                        if stoch_k_value > stoch_overbought and stoch_k_value < stoch_k[-2]: # K线从超买区下穿D线或K线自身拐头向下
                            trend_strength -= 0.75
                            trend_signals.append(f"随机指标超买且下降 ({stoch_k_value:.2f})，震荡市看空")
                        elif stoch_k_value < stoch_oversold and stoch_k_value > stoch_k[-2]: # K线从超卖区上穿D线或K线自身拐头向上
                            trend_strength += 0.75
                            trend_signals.append(f"随机指标超卖且上升 ({stoch_k_value:.2f})，震荡市看多")
                    else: # 强趋势市场，随机指标信号降权或忽略
                        if stoch_k_value > stoch_overbought:
                            trend_signals.append(f"随机指标超买({stoch_k_value:.2f})，强趋势市忽略")
                        elif stoch_k_value < stoch_oversold:
                            trend_signals.append(f"随机指标超卖({stoch_k_value:.2f})，强趋势市忽略")
                    
                    # CCI信号 - 在震荡市中权重较高
                    cci_value = cci[-1]
                    cci_overbought = getattr(self, 'cci_overbought', 100)
                    cci_oversold = getattr(self, 'cci_oversold', -100)
                    if market_state == "RANGING_WEAK_TREND":
                        if cci_value > cci_overbought:
                            trend_strength -= 0.75
                            trend_signals.append(f"CCI超买 ({cci_value:.2f})，震荡市看空")
                        elif cci_value < cci_oversold:
                            trend_strength += 0.75
                            trend_signals.append(f"CCI超卖 ({cci_value:.2f})，震荡市看多")
                    else: # 强趋势市场，CCI信号降权或忽略
                         if cci_value > cci_overbought:
                            trend_signals.append(f"CCI超买({cci_value:.2f})，强趋势市忽略")
                         elif cci_value < cci_oversold:
                            trend_signals.append(f"CCI超卖({cci_value:.2f})，强趋势市忽略")

                    # MFI信号 - 在震荡市中权重较高
                    mfi_value = mfi[-1]
                    mfi_overbought = getattr(self, 'mfi_overbought', 80)
                    mfi_oversold = getattr(self, 'mfi_oversold', 20)
                    if market_state == "RANGING_WEAK_TREND":
                        if mfi_value > mfi_overbought:
                            trend_strength -= 0.75
                            trend_signals.append(f"MFI超买 ({mfi_value:.2f})，震荡市看空")
                        elif mfi_value < mfi_oversold:
                            trend_strength += 0.75
                            trend_signals.append(f"MFI超卖 ({mfi_value:.2f})，震荡市看多")
                    else: # 强趋势市场，MFI信号降权或忽略
                        if mfi_value > mfi_overbought:
                            trend_signals.append(f"MFI超买({mfi_value:.2f})，强趋势市忽略")
                        elif mfi_value < mfi_oversold:
                            trend_signals.append(f"MFI超卖({mfi_value:.2f})，强趋势市忽略")

                    # 成交量趋势 (OBV) - 可作为辅助，强趋势下顺势成交量放大是确认
                    if obv[-1] > obv[-2]: # 成交量放大
                        if market_state == "STRONG_UPTREND":
                            trend_strength += 0.5
                            trend_signals.append("OBV成交量放大，配合强上涨趋势")
                        elif market_state == "STRONG_DOWNTREND":
                            # 下跌时成交量放大，可能加速下跌，也可能恐慌抛售接近尾声，这里稍微复杂
                            # 暂定为中性或轻微负面，因为通常下跌放量被视为空头力量强
                            trend_strength -= 0.25 
                            trend_signals.append("OBV成交量放大，配合强下跌趋势")
                        else: # 震荡市成交量变化意义不大
                            trend_signals.append("OBV成交量变化，震荡市中性")
                    elif obv[-1] < obv[-2]: # 成交量萎缩
                        if market_state == "STRONG_UPTREND":
                            trend_strength -= 0.25 # 上涨缩量，趋势可能减弱
                            trend_signals.append("OBV成交量萎缩，强上涨趋势中警惕")
                        elif market_state == "STRONG_DOWNTREND":
                            trend_strength += 0.25 # 下跌缩量，趋势可能减弱
                            trend_signals.append("OBV成交量萎缩，强下跌趋势中警惕")
                        else:
                            trend_signals.append("OBV成交量变化，震荡市中性")
                    
                    # 记录情感分析结果
                    self.log_trading(f"新闻情感分析：整体得分 {avg_news_sentiment:.2f}，趋势判断 {news_trend}", level='info')
                    self.log_trading(f"分析了 {len(all_articles[:10])} 条新闻，包含监管动态和机构参与相关内容", level='info')
                    
                    # 将新闻情感纳入趋势强度计算
                    if news_trend == "看多":
                        trend_strength += 2  # 将权重从1增加到2
                        trend_signals.append("新闻情感明显偏多")
                    elif news_trend == "看空":
                        trend_strength -= 2  # 将权重从1增加到2
                        trend_signals.append("新闻情感明显偏空")
                    
                    # 计算最终趋势强度
                    final_trend_strength = float(trend_strength)  # 确保是浮点数
                    
                    # 准备AI分析提示
                    prompt = f"""作为一个专业的加密货币分析师，请基于以下信息进行详细分析，并严格按照指定格式输出结果。
请特别注意：
1. 趋势判断必须明确且及时，不要犹豫
2. 价格设置必须严格符合要求，不得有任何偏差
3. 重点关注特朗普和美联储新闻对加密货币的影响

当前市场信息：
- 货币对：{symbol}/USDT
- 当前价格：{current_price}
- **判定市场状态**：{market_state} (基于ADX({getattr(self, 'adx_period', 14)})={adx_value:.2f}, +DI={plus_di_value:.2f}, -DI={minus_di_value:.2f}, 阈值={current_adx_threshold})
- RSI({getattr(self, 'rsi_period', 14)}): {rsi[-1]:.2f} {'(超买)' if rsi[-1] > getattr(self, 'rsi_overbought', 70) else '(超卖)' if rsi[-1] < getattr(self, 'rsi_oversold', 30) else ''}
- MACD({getattr(self, 'macd_fast', 12)},{getattr(self, 'macd_slow', 26)},{getattr(self, 'macd_signal', 9)}): {macd[-1]:.2f} {'(金叉)' if macd[-1] > signal[-1] else '(死叉)'}
- 布林带({getattr(self, 'bb_period', 20)},{getattr(self, 'bb_std', 2.0)}): 上轨 {upper[-1]:.2f}, 中轨 {middle[-1]:.2f}, 下轨 {lower[-1]:.2f}
  {'(突破上轨)' if current_price > upper[-1] else '(突破下轨)' if current_price < lower[-1] else '(区间内)'}
- EMA: {getattr(self, 'ema_short', 20)}日 {ema20[-1]:.2f}, {getattr(self, 'ema_medium', 50)}日 {ema50[-1]:.2f}
  {'(黄金交叉)' if ema20[-1] > ema50[-1] and ema20[-2] <= ema50[-2] else '(死亡交叉)' if ema20[-1] < ema50[-1] and ema20[-2] >= ema50[-2] else ''}
- 随机指标: K({stoch_k[-1]:.2f}) D({stoch_d[-1]:.2f}) {'(超买区)' if stoch_k[-1] > getattr(self, 'stoch_overbought', 80) else '(超卖区)' if stoch_k[-1] < getattr(self, 'stoch_oversold', 20) else ''}
- ADX({getattr(self, 'adx_period', 14)}): {adx[-1]:.2f} (+DI: {di_plus[-1]:.2f}, -DI: {di_minus[-1]:.2f}) {'(趋势强)' if adx[-1] > getattr(self, 'adx_threshold', 25) else '(趋势弱)'}
- CCI({getattr(self, 'cci_period', 20)}): {cci[-1]:.2f} {'(超买)' if cci[-1] > getattr(self, 'cci_overbought', 100) else '(超卖)' if cci[-1] < getattr(self, 'cci_oversold', -100) else ''}
- MFI({getattr(self, 'mfi_period', 14)}): {mfi[-1]:.2f} {'(超买)' if mfi[-1] > getattr(self, 'mfi_overbought', 80) else '(超卖区)' if mfi[-1] < getattr(self, 'mfi_oversold', 20) else ''}

趋势判断参考：
1. 技术面分析：
   **请结合当前市场状态 ({market_state}) 解读以下指标：**
   - **强趋势 ({str(market_state == "STRONG_UPTREND" or market_state == "STRONG_DOWNTREND")})**：
     - 优先关注与趋势同向的信号（如MACD顺势交叉，价格沿布林带轨道运行）。
     - 振荡指标（RSI, Stochastic, CCI, MFI）的超买/超卖可能指示趋势持续，而非立即反转，需谨慎对待其反转信号。
   - **震荡/弱趋势 ({str(market_state == "RANGING_WEAK_TREND")})**：
     - 振荡指标的超买/超卖信号权重增加，可用于寻找潜在的反转点。
     - MACD和布林带的信号需警惕假突破。

   - RSI > {getattr(self, 'rsi_overbought', 70)} 且价格突破布林上轨，倾向看空 (震荡市更可靠)
   - RSI < {getattr(self, 'rsi_oversold', 30)} 且价格突破布林下轨，倾向看多 (震荡市更可靠)
   - MACD金叉且RSI回升，倾向看多
   - MACD死叉且RSI下降，倾向看空
   - EMA{getattr(self, 'ema_short', 20)}上穿EMA{getattr(self, 'ema_medium', 50)}形成黄金交叉，倾向看多
   - EMA{getattr(self, 'ema_short', 20)}下穿EMA{getattr(self, 'ema_medium', 50)}形成死亡交叉，倾向看空
   - ADX > {getattr(self, 'adx_threshold', 25)}表示趋势强，结合其他指标判断方向
   - 随机指标K值和D值在超买区（>{getattr(self, 'stoch_overbought', 80)}）且开始下降，倾向看空
   - 随机指标K值和D值在超卖区（<{getattr(self, 'stoch_oversold', 20)}）且开始上升，倾向看多

2. 新闻面分析：
   - 特朗普相关新闻：
     * 关注其对加密货币的态度和言论
     * 其政策主张对加密货币市场的潜在影响
     * 是否涉及对加密货币的监管态度
   
   - 美联储相关新闻：
     * 利率政策变化及预期
     * 通胀数据及应对措施
     * 货币政策声明对市场的影响
     * 美元走势对加密货币的影响

   - 监管动态：
     * SEC、CFTC等监管机构的政策发布与声明
     * 全球主要国家加密货币监管法规变化
     * 对交易所的监管措施与合规要求
     * 反洗钱和KYC政策的实施情况
   
   - 机构参与度：
     * 大型机构（如BlackRock、摩根大通、高盛等）投资加密货币的动向
     * ETF批准或拒绝的最新进展
     * 传统金融机构进入加密领域的战略
     * 机构持仓量的变化趋势

{news_summary}

请严格按照以下规则设置价格：

1. 如果建议做多：
   - 入场价必须接近但略低于当前价格（差距不超过0.5%）
   - 止盈价必须设置在入场价的0.5-1%上方（包含边界值）
   - 止损价必须设置在入场价的3-5%下方（包含3%和5%的边界值）

2. 如果建议做空：
   - 入场价必须接近但略高于当前价格（差距不超过0.5%）
   - 止盈价必须设置在入场价的0.5-1%下方（包含边界值）
   - 止损价必须设置在入场价的3-5%上方（包含3%和5%的边界值）

请按照以下格式输出：

市场趋势：[看多/看空]
入场价位：[具体数字] USDT
止盈价位：[具体数字] USDT
止损价位：[具体数字] USDT

详细分析：
1. 技术面分析：
[详细分析内容，包括RSI、MACD、布林带、EMA、随机指标和ADX的具体信号]

2. 新闻影响分析：
[详细分析内容，重点关注特朗普和美联储政策的影响]

3. 价格设置依据：
[解释入场价、止盈价和止损价的设置原因，必须包含具体的百分比计算]

4. 潜在风险提示：
[详细说明可能影响交易的风险因素]

注意：
1. 趋势判断必须明确，不要模棱两可
2. 所有价格必须是具体的数字，且严格符合上述百分比范围要求
3. 价格必须基于当前市场价格进行合理设置
4. 分析要客观专业，并提供具体理由
5. 严格按照上述格式输出，不要改变格式
"""

                    # 调用DeepSeek AI进行分析
                    headers = {
                        'Authorization': f'Bearer {self.ai_api_key.text()}',
                        'Content-Type': 'application/json'
                    }
                    
                    api_url = "https://api.deepseek.com/v1/chat/completions"
                    api_data = {
                        "model": "deepseek-chat",
                        "messages": [{"role": "user", "content": prompt}],
                        "temperature": 0.7,
                        "timeout": timeout
                    }
                    
                    # 使用session进行请求，增加重试机制
                    session = requests.Session()
                    retries = Retry(
                        total=max_retries,
                        backoff_factor=1,
                        status_forcelist=[500, 502, 503, 504]
                    )
                    session.mount('https://', HTTPAdapter(max_retries=retries))
                    
                    response = session.post(
                        api_url, 
                        json=api_data, 
                        headers=headers, 
                        timeout=(30, timeout)  # (连接超时, 读取超时)
                    )
                    
                    if response.status_code != 200:
                        raise Exception(f"AI分析失败: {response.text}")
                    
                    analysis = response.json()['choices'][0]['message']['content']
                    
                    # 解析分析结果
                    trend_match = re.search(r"市场趋势：\s*([看多|看空]+)", analysis)
                    entry_match = re.search(r"入场价位：\s*(\d+\.?\d*)\s*USDT", analysis)
                    tp_match = re.search(r"止盈价位：\s*(\d+\.?\d*)\s*USDT", analysis)
                    sl_match = re.search(r"止损价位：\s*(\d+\.?\d*)\s*USDT", analysis)
                    
                    if all([trend_match, entry_match, tp_match, sl_match]):
                        result = {
                            'trend': trend_match.group(1),
                            'entry_price': entry_match.group(1),
                            'tp_price': tp_match.group(1),
                            'sl_price': sl_match.group(1),
                            'analysis': analysis,
                            'market_state': market_state,  # 添加市场状态
                            'trend_strength': trend_strength,  # 添加趋势强度
                            'signals': trend_signals,  # 添加趋势信号列表
                            'sentiment_score': avg_news_sentiment,  # 添加新闻情感分数
                            'ai_recommendation': trend_match.group(1) if trend_match else None  # AI建议的多空方向
                        }
                        
                        self.log_trading(f"分析结果: {result['trend']}, 市场状态: {market_state}, 趋势强度: {trend_strength:.2f}")
                        return result
                    else:
                        self.log_trading("无法从AI分析结果中提取完整信息")
                        return None
                    
                except requests.exceptions.Timeout:
                    if attempt < max_retries - 1:
                        backoff_time = retry_delay * (2 ** attempt)  # 指数退避
                        self.log_trading(f"API请求超时，{backoff_time}秒后重试... (尝试 {attempt + 1}/{max_retries})")
                        time.sleep(backoff_time)
                        continue
                    else:
                        self.log_trading("API请求超时，已达到最大重试次数")
                        return None
                    
                except requests.exceptions.RequestException as e:
                    if attempt < max_retries - 1:
                        self.log_trading(f"网络请求异常: {str(e)}，将在{retry_delay}秒后重试...")
                        time.sleep(retry_delay)
                        continue
                    else:
                        self.log_trading(f"网络请求失败，已达到最大重试次数: {str(e)}")
                        return None
                    
                except Exception as e:
                    self.log_trading(f"获取交易信号失败: {str(e)}")
                    return None
                
            return None

        except Exception as e:
            self.log_trading(f"获取交易信号失败: {str(e)}")
            return None

    def place_ai_order(self, symbol, side, amount, entry_price, tp_price, sl_price):
        """下AI永续合约订单 - 优化止盈止损逻辑"""
        try:
            # 确保所有价格都是有效的数字
            if not all(isinstance(x, (int, float)) for x in [entry_price, tp_price, sl_price]):
                raise ValueError("价格必须是有效的数字")
            
            # 验证价格的合理性
            if entry_price <= 0 or tp_price <= 0 or sl_price <= 0:
                raise ValueError("价格必须大于0")

            # 决定使用的入场价格
            if self.use_ai_price_radio.isChecked():
                # 使用AI分析的入场价格
                entry_price_to_use = entry_price
                self.log_trading(f"使用AI分析入场价格: {entry_price_to_use}")
            else:
                # 使用当前市价作为入场价格
                ticker = self.exchange.fetch_ticker(symbol)
                entry_price_to_use = ticker['last']
                self.log_trading(f"使用当前市价: {entry_price_to_use}")
            
            # 新增选项：是否使用AI建议的止盈止损价格
            use_ai_tp_sl = False  # 这应该是UI中的一个选项，例如复选框
            
            # 统一止盈止损价格计算逻辑
            if use_ai_tp_sl and self.use_ai_price_radio.isChecked():
                # 使用AI建议的止盈止损价格
                tp_price_to_use = tp_price
                sl_price_to_use = sl_price
                self.log_trading("使用AI建议的止盈止损价格")
            else:
                # 使用用户设置的止盈止损百分比
                if side == 'buy':
                    tp_price_to_use = entry_price_to_use * (1 + self.tp_percent / 100)
                    sl_price_to_use = entry_price_to_use * (1 - self.sl_percent / 100)
                else:  # side == 'sell'
                    tp_price_to_use = entry_price_to_use * (1 - self.tp_percent / 100)
                    sl_price_to_use = entry_price_to_use * (1 + self.sl_percent / 100)
                
                self.log_trading(
                    f"使用自定义止盈止损设置:\n"
                    f"止盈比例: {self.tp_percent}% (价格: {tp_price_to_use:.2f})\n"
                    f"止损比例: {self.sl_percent}% (价格: {sl_price_to_use:.2f})"
                )
            
            # 构造永续合约交易对
            base_quote = symbol.split('/')
            base, quote = base_quote[0], base_quote[1]
            swap_symbol = f"{base}-{quote}-SWAP"

            # 获取当前杠杆倍数和设置杠杆
            leverage = self.ai_leverage_spinbox.value()
            pos_side = 'long' if side == 'buy' else 'short'
            
            try:
                self.exchange.private_post_account_set_leverage({
                    'instId': swap_symbol,
                    'lever': str(leverage),
                    'mgnMode': 'isolated',
                    'posSide': pos_side
                })
                self.log_trading(f"已设置{pos_side}方向杠杆倍数: {leverage}x")
            except Exception as e:
                self.log_trading(f"设置杠杆倍数失败: {str(e)}")
                return None

            # 获取市场信息
            market = self.exchange.market(swap_symbol)
            min_amount = 0.01  # OKX永续合约最小交易量

            # 格式化数量
            quantity = max(min_amount, round(amount / min_amount) * min_amount)

            # 生成订单ID
            order_id = f"AI{datetime.now().strftime('%Y%m%d%H%M%S')}"

            # 根据用户选择决定订单类型
            order_type = 'market' if self.market_order_radio.isChecked() else 'limit'
            
            # 设置基本订单参数
            params = {
                "instId": swap_symbol,
                "tdMode": "isolated",  # 逐仓模式
                "posSide": pos_side,
                "lever": str(leverage),
                "clOrdId": order_id,
                "sz": str(quantity)
            }
            
            # 根据订单类型设置不同参数
            if order_type == 'market':
                params["ordType"] = "market"
            else:  # 限价单
                params["ordType"] = "limit"
                params["px"] = str(entry_price)  # 限价单需要指定价格
                
            # 设置止盈止损参数
            params.update({
                "tpTriggerPx": str(tp_price_to_use),
                "tpOrdPx": str(tp_price_to_use),
                "slTriggerPx": str(sl_price_to_use),
                "slOrdPx": str(sl_price_to_use)
            })

            # 记录下单信息
            self.log_trading(f"下单详情:\n"
                            f"合约: {params['instId']}\n"
                            f"方向: {side.upper()} {params['posSide'].upper()}\n"
                            f"数量: {quantity} 张\n"
                            f"类型: {('市价单' if order_type == 'market' else '限价单')}\n"
                            f"{'入场价: ' + str(entry_price_to_use) if order_type == 'limit' else ''}\n"
                            f"止盈价: {tp_price_to_use:.1f}\n"
                            f"止损价: {sl_price_to_use:.1f}\n"
                            f"杠杆倍数: {params['lever']}x\n"
                            f"保证金模式: {params['tdMode']}")

            # 下单
            order = self.exchange.create_order(
                symbol=swap_symbol,
                type=order_type,
                side=side,
                amount=float(quantity),
                price=entry_price if order_type == 'limit' else None,
                params=params
            )

            # 记录订单详情
            log_msg = (
                f"永续合约{('市价单' if order_type == 'market' else '限价单')}已提交:\n"
                f"合约: {params['instId']}\n"
                f"方向: {side.upper()} {params['posSide'].upper()}\n"
                f"数量: {quantity}张\n"
                f"杠杆: {params['lever']}x\n"
                f"入场价: {('市价' if order_type == 'market' else entry_price_to_use)}\n"
                f"止盈价: {tp_price_to_use:.2f} ({(abs(tp_price_to_use - entry_price_to_use) / entry_price_to_use * 100):.2f}%)\n"
                f"止损价: {sl_price_to_use:.2f} ({(abs(sl_price_to_use - entry_price_to_use) / entry_price_to_use * 100):.2f}%)\n"
                f"{'使用AI分析价格' if self.use_ai_price_radio.isChecked() else '使用当前市价'}\n"
                f"{'使用AI建议止盈止损' if (use_ai_tp_sl and self.use_ai_price_radio.isChecked()) else '使用设置的止盈止损百分比'}"
            )
            self.log_trading(log_msg)
            return order

        except ValueError as ve:
            error_msg = f"参数验证失败: {str(ve)}"
            self.log_trading(error_msg)
            return None
        except Exception as e:
            error_msg = f"下单失败: {str(e)}"
            self.log_trading(error_msg)
            self.log_trading("错误详情: " + str(e.__class__.__name__))
            if hasattr(e, 'response'):
                self.log_trading("API响应: " + str(e.response.text))
            return None

    def monitor_order_status(self, order_id, symbol):
        """监控订单状态"""
        def check_status():
            try:
                max_retries = 3  # 最大重试次数
                retry_count = 0
                
                while retry_count < max_retries:
                    try:
                        order = self.exchange.fetch_order(order_id, symbol)
                        status = order['status']
                        
                        if status == 'closed':
                            self.update_log_signal.emit(f"订单 {order_id} 已成交")
                            return
                        elif status == 'canceled':
                            self.update_log_signal.emit(f"订单 {order_id} 已取消")
                            return
                        
                        time.sleep(2)  # 每2秒检查一次
                        retry_count += 1
                        
                    except OrderNotFound as e:
                        self.update_log_signal.emit(f"订单 {order_id} 不存在或已完成")
                        return
                    except Exception as e:
                        if 'Order does not exist' in str(e):
                            self.update_log_signal.emit(f"订单 {order_id} 已不存在")
                            return
                        # 其他错误重试
                        time.sleep(1)
                        retry_count += 1
                
                self.update_log_signal.emit(f"订单 {order_id} 检查超时")
                
            except Exception as e:
                self.update_log_signal.emit(f"订单监控错误: {str(e)}")
                
        # 启动监控线程
        threading.Thread(target=check_status, daemon=True).start()

    def update_ai_trading_price(self):
        """更新AI交易界面的实时价格"""
        try:
            symbol = self.ai_trading_symbol_combo.currentText()
            ticker = self.exchange.fetch_ticker(symbol)
            current_price = round(ticker['last'], 2)  # 保留两位小数
            
            # 更新当前价格
            self.price_display.setText(f"当前价格: {current_price:.2f} USDT")
            
            # 计算并显示实时波动
            if hasattr(self, 'last_trigger_price') and self.last_trigger_price is not None:
                volatility = abs((current_price - self.last_trigger_price) / self.last_trigger_price * 100)
                volatility = round(volatility, 2)  # 保留两位小数
                
                # 设置波动显示颜色
                volatility_color = "#F23645" if volatility >= 0.25 else "#FFFFFF"
                volatility_text = (f"当前波动: {volatility:.2f}% "
                                 f"{'(将触发分析)' if volatility >= 0.25 else ''}")
                
                self.volatility_display.setText(volatility_text)
                self.volatility_display.setStyleSheet(f"color: {volatility_color}; font-size: 14px;")
            else:
                self.volatility_display.setText("等待首次分析...")
                self.volatility_display.setStyleSheet("color: #FFFFFF; font-size: 14px;")
            
            # 计算24小时涨幅
            open_price = ticker['open']
            if open_price > 0:
                change_percent = (current_price - open_price) / open_price * 100
                change_color = "#2EBD85" if change_percent >= 0 else "#F23645"
                self.change_display.setText(f"24小时涨幅: {change_percent:.2f}%")
                self.change_display.setStyleSheet(f"font-size: 14px; color: {change_color};")
            
        except Exception as e:
            self.log_trading(f"更新AI交易价格失败: {str(e)}", level='error')

    def log_trading(self, message, level='info'):
        """记录交易日志，可从任意线程调用"""
        log_message = f"[{level.upper()}] {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} - {message}"
        self.update_log_signal.emit(log_message)

    def log_trading_main_thread(self, message):
        """在主线程中更新日志（由信号触发）"""
        if hasattr(self, 'trading_log') and self.trading_log is not None:
            # 解析日志级别，设置不同颜色
            if '[INFO]' in message:
                color = '#2EBD85'  # 绿色
            elif '[WARNING]' in message:
                color = '#FFA500'  # 橙色
            elif '[ERROR]' in message:
                color = '#F23645'  # 红色
            else:
                color = '#FFFFFF'  # 白色
            
            # 使用HTML格式设置颜色
            html_message = f'<span style="color: {color};">{message}</span>'
            self.trading_log.appendHtml(html_message)

    def save_trading_log(self):
        """保存交易日志到文件"""
        try:
            log_content = self.trading_log.toHtml()
            if not log_content:
                self.show_message_signal.emit("警告", "没有可保存的日志内容！", "warning")
                return
            
            # 获取当前时间作为文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = f"trading_log_{timestamp}.html"
            
            # 保存文件
            with open(file_name, 'w', encoding='utf-8') as f:
                f.write(log_content)
            
            self.show_message_signal.emit("成功", f"日志已保存为 {file_name}", "info")
            
        except Exception as e:
            self.show_message_signal.emit("错误", f"保存日志失败: {str(e)}", "error")

    def delete_trading_log(self):
        """删除当前交易日志"""
        try:
            # 使用自定义对话框替代 QMessageBox
            dialog = QDialog(self)
            dialog.setWindowTitle("确认")
            layout = QVBoxLayout(dialog)
            
            label = QLabel("确定要删除当前交易日志吗？")
            layout.addWidget(label)
            
            button_box = QHBoxLayout()
            yes_button = QPushButton("是")
            no_button = QPushButton("否")
            
            yes_button.clicked.connect(lambda: self._delete_log_confirmed(dialog))
            no_button.clicked.connect(dialog.reject)
            
            button_box.addWidget(yes_button)
            button_box.addWidget(no_button)
            layout.addLayout(button_box)
            
            dialog.exec()
            
        except Exception as e:
            self.show_message_signal.emit("错误", f"删除日志失败: {str(e)}", "error")
            
    def _delete_log_confirmed(self, dialog):
        """确认删除日志"""
        self.trading_log.clear()
        dialog.accept()
        self.show_message_signal.emit("成功", "交易日志已删除", "info")

    def show_message_box(self, title, message, msg_type='info'):
        """在主线程中显示消息框"""
        if msg_type == 'warning':
            QMessageBox.warning(self, title, message)
        elif msg_type == 'error':
            QMessageBox.critical(self, title, message)
        else:
            QMessageBox.information(self, title, message)

    def load_config(self):
        """加载配置文件"""
        try:
            with open('config.json', 'r') as f:
                config = json.load(f)
                # 加载配置项
                self.tp_percent = config.get('tp_percent', 1.5)  # 默认值为1.5%
                self.sl_percent = config.get('sl_percent', 1.5)  # 默认值为1.5%
                
                # 更新UI控件
                self.tp_spinbox.setValue(self.tp_percent)
                self.sl_spinbox.setValue(self.sl_percent)
        except FileNotFoundError:
            # 使用默认配置
            self.tp_percent = 1.5  # 默认值为1.5%
            self.sl_percent = 1.5  # 默认值为1.5%
            
            # 保存默认配置到文件
            with open('config.json', 'w') as f:
                json.dump({
                    'tp_percent': self.tp_percent,
                    'sl_percent': self.sl_percent
                }, f, indent=4)
                
    def update_trading_stats(self):
        """更新交易统计"""
        # 添加交易次数、成功率等统计
        pass

    def get_market_data(self, symbol):
        """缓存市场数据"""
        cache = self._market_data_cache.get(symbol)
        if cache is None:
            cache = CachedData()
            self._market_data_cache[symbol] = cache
            
        if not cache.is_valid():
            # 缓存过期或不存在，获取新数据
            cache.data = self.exchange.fetch_ticker(symbol)
            cache.timestamp = datetime.now()
            
        return cache.data

    def confirm_trade(self, order_details):
        """交易确认"""
        reply = QMessageBox.question(self, "确认交易",
                                   f"确认执行以下交易？\n{order_details}",
                                   QMessageBox.Yes | QMessageBox.No)
        return reply == QMessageBox.Yes

    def _generate_technical_summary(self, current_price, dfs, indicators):
        """生成技术分析总结"""
        try:
            # 初始化可能在模板字符串中使用的变量，确保它们在所有代码路径中都有值
            market_state = "RANGING_WEAK_TREND"  # 默认为震荡或弱趋势
            adx_value = 0.0
            plus_di_value = 0.0
            minus_di_value = 0.0
            current_adx_threshold = getattr(self, 'adx_threshold', 25)
            
            # 解包关键指标
            df = dfs.get('1h')
            
            if len(df) < 50:
                return "数据不足，无法生成技术分析摘要"
                
            close_prices = df['close'].values
            high_prices = df['high'].values
            low_prices = df['low'].values
            
            # 计算技术指标值
            rsi = indicators.get('rsi', talib.RSI(close_prices))
            
            # 计算ADX相关指标
            adx = indicators.get('adx', talib.ADX(high_prices, low_prices, close_prices, timeperiod=getattr(self, 'adx_period', 14)))
            di_plus = indicators.get('di_plus', talib.PLUS_DI(high_prices, low_prices, close_prices, timeperiod=getattr(self, 'adx_period', 14)))
            di_minus = indicators.get('di_minus', talib.MINUS_DI(high_prices, low_prices, close_prices, timeperiod=getattr(self, 'adx_period', 14)))
            
            # 判断市场状态
            market_state = "RANGING_WEAK_TREND"  # 默认为震荡或弱趋势
            adx_value = adx[-1]
            plus_di_value = di_plus[-1]
            minus_di_value = di_minus[-1]
            current_adx_threshold = getattr(self, 'adx_threshold', 25)

            if adx_value > current_adx_threshold:
                if plus_di_value > minus_di_value:
                    market_state = "STRONG_UPTREND"
                elif minus_di_value > plus_di_value:
                    market_state = "STRONG_DOWNTREND"
            
            # 继续计算其他指标
            macd, signal, hist = indicators.get('macd', talib.MACD(close_prices))
            upper, middle, lower = indicators.get('bollinger', talib.BBANDS(
                close_prices, 
                timeperiod=getattr(self, 'bb_period', 20),
                nbdevup=getattr(self, 'bb_std', 2.0),
                nbdevdn=getattr(self, 'bb_std', 2.0)
            ))
            ema20 = indicators.get('ema20', talib.EMA(close_prices, timeperiod=getattr(self, 'ema_short', 20)))
            ema50 = indicators.get('ema50', talib.EMA(close_prices, timeperiod=getattr(self, 'ema_medium', 50)))
            ema200 = indicators.get('ema200', talib.EMA(close_prices, timeperiod=getattr(self, 'ema_long', 200)))
            
            # 解释市场状态
            market_state_desc = {
                "STRONG_UPTREND": "强劲上涨趋势",
                "STRONG_DOWNTREND": "强劲下跌趋势",
                "RANGING_WEAK_TREND": "震荡或弱趋势"
            }.get(market_state, "未知")
            
            # 生成摘要，基于市场状态动态解读指标
            summary = f"【市场状态】{market_state_desc} (ADX: {adx_value:.2f} +DI: {plus_di_value:.2f} -DI: {minus_di_value:.2f})\n"
            
            # 价格相对于均线
            summary += "【均线分析】"
            above_ema20 = current_price > ema20[-1]
            above_ema50 = current_price > ema50[-1]
            above_ema200 = current_price > ema200[-1]
            
            if above_ema20 and above_ema50 and above_ema200:
                summary += "价格位于所有均线之上，整体看多。"
            elif not above_ema20 and not above_ema50 and not above_ema200:
                summary += "价格位于所有均线之下，整体看空。"
            else:
                if above_ema20 and above_ema50:
                    summary += "价格位于中短期均线之上，中期看多。"
                elif not above_ema20 and not above_ema50:
                    summary += "价格位于中短期均线之下，中期看空。"
                else:
                    summary += "价格在均线之间波动，趋势不明确。"
            
            # 均线交叉
            if ema20[-1] > ema50[-1] and ema20[-2] <= ema50[-2]:
                if market_state == "STRONG_UPTREND":
                    summary += " 短期均线上穿中期均线(金叉)，强趋势中信号强烈，大概率继续上行。"
                else:
                    summary += " 短期均线上穿中期均线(金叉)，可能开启上升趋势。"
            elif ema20[-1] < ema50[-1] and ema20[-2] >= ema50[-2]:
                if market_state == "STRONG_DOWNTREND":
                    summary += " 短期均线下穿中期均线(死叉)，强趋势中信号强烈，大概率继续下行。"
                else:
                    summary += " 短期均线下穿中期均线(死叉)，可能开启下降趋势。"
                
            # RSI分析，考虑市场状态
            summary += "\n【RSI分析】"
            rsi_value = rsi[-1]
            rsi_overbought = getattr(self, 'rsi_overbought', 70)
            rsi_oversold = getattr(self, 'rsi_oversold', 30)
            
            if market_state == "STRONG_UPTREND":
                if rsi_value > rsi_overbought:
                    summary += f"RSI位于超买区域({rsi_value:.2f})，但在强上涨趋势中，这常常表示趋势持续而非即将反转。"
                else:
                    summary += f"RSI值为{rsi_value:.2f}，在强上涨趋势中处于健康水平。"
            elif market_state == "STRONG_DOWNTREND":
                if rsi_value < rsi_oversold:
                    summary += f"RSI位于超卖区域({rsi_value:.2f})，但在强下跌趋势中，这可能表示趋势继续而非即将反转。"
                else:
                    summary += f"RSI值为{rsi_value:.2f}，在强下跌趋势中处于正常水平。"
            else: # RANGING_WEAK_TREND
                if rsi_value > rsi_overbought:
                    summary += f"RSI位于超买区域({rsi_value:.2f})，震荡市场中可能即将回调。"
                elif rsi_value < rsi_oversold:
                    summary += f"RSI位于超卖区域({rsi_value:.2f})，震荡市场中可能即将反弹。"
                else:
                    summary += f"RSI值为{rsi_value:.2f}，处于中性区域。"
                    
            # MACD分析，考虑市场状态
            summary += "\n【MACD分析】"
            if macd[-1] > signal[-1] and macd[-2] <= signal[-2]:
                if market_state == "STRONG_UPTREND":
                    summary += "MACD金叉，在强上涨趋势中是强烈的看多信号，建议继续持有多单或考虑加仓。"
                elif market_state == "STRONG_DOWNTREND":
                    summary += "MACD金叉，但在强下跌趋势中需谨慎对待，可能是短期反弹而非趋势反转。"
                else:
                    summary += "MACD金叉，震荡市场中是较好的买入信号。"
            elif macd[-1] < signal[-1] and macd[-2] >= signal[-2]:
                if market_state == "STRONG_DOWNTREND":
                    summary += "MACD死叉，在强下跌趋势中是强烈的看空信号，建议继续持有空单或考虑加仓。"
                elif market_state == "STRONG_UPTREND":
                    summary += "MACD死叉，但在强上涨趋势中需谨慎对待，可能是短期回调而非趋势反转。"
                else:
                    summary += "MACD死叉，震荡市场中是较好的卖出信号。"
            elif macd[-1] > signal[-1]:
                summary += "MACD位于信号线上方，动能偏向多头。"
            else:
                summary += "MACD位于信号线下方，动能偏向空头。"
                
            # 布林带分析，考虑市场状态
            summary += "\n【布林带分析】"
            if market_state == "STRONG_UPTREND":
                if current_price > upper[-1]:
                    summary += "价格突破布林上轨，在强上涨趋势中表示趋势加速，而非超买。"
                else:
                    summary += f"布林带显示上行趋势，当前价格为{current_price:.2f}，上轨{upper[-1]:.2f}，中轨{middle[-1]:.2f}，下轨{lower[-1]:.2f}。"
            elif market_state == "STRONG_DOWNTREND":
                if current_price < lower[-1]:
                    summary += "价格突破布林下轨，在强下跌趋势中表示趋势加速，而非超卖。"
                else:
                    summary += f"布林带显示下行趋势，当前价格为{current_price:.2f}，上轨{upper[-1]:.2f}，中轨{middle[-1]:.2f}，下轨{lower[-1]:.2f}。"
            else:
                if current_price > upper[-1]:
                    summary += "价格突破布林上轨，在震荡市场中通常表示超买，可能即将回调。"
                elif current_price < lower[-1]:
                    summary += "价格突破布林下轨，在震荡市场中通常表示超卖，可能即将反弹。"
                else:
                    width = (upper[-1] - lower[-1]) / middle[-1]
                    if width < 0.1:
                        summary += "布林带收窄，可能即将突破。"
                    else:
                        position = (current_price - lower[-1]) / (upper[-1] - lower[-1])
                        if position > 0.8:
                            summary += "价格接近布林上轨，偏向看空。"
                        elif position < 0.2:
                            summary += "价格接近布林下轨，偏向看多。"
                        else:
                            summary += "价格在布林带中间区域，趋势中性。"
                            
            return summary
        except Exception as e:
            self.log_trading(f"生成技术分析摘要时出错: {str(e)}", level='error')
            return "技术分析摘要生成失败"

    def on_leverage_changed(self, value):
        """处理杠杆倍数变化"""
        try:
            symbol = self.ai_trading_symbol_combo.currentText()
            swap_symbol = f"{symbol.split('/')[0]}-{symbol.split('/')[1]}-SWAP"
            
            # 使用OKX特定的API设置杠杆倍数
            try:
                # 先检查是否有未完成的订单
                orders = self.exchange.fetch_open_orders(symbol=swap_symbol)
                if orders:
                    self.log_trading("请先取消所有未完成订单再修改杠杆", level='warning')
                    # 回滚杠杆设置
                    self.ai_leverage_spinbox.blockSignals(True)
                    self.ai_leverage_spinbox.setValue(self.ai_leverage_spinbox.value())
                    self.ai_leverage_spinbox.blockSignals(False)
                    return
                    
                # 需要同时设置多空两个方向的杠杆
                for pos_side in ['long', 'short']:
                    try:
                        self.exchange.private_post_account_set_leverage({
                            'instId': swap_symbol,
                            'lever': str(value),
                            'mgnMode': 'isolated',  # 逐仓模式
                            'posSide': pos_side    # 设置持仓方向
                        })
                    except Exception as e:
                        if "Leverage can't be modified" in str(e):
                            self.log_trading("请先取消所有未完成订单再修改杠杆", level='warning')
                        else:
                            self.log_trading(f"设置{pos_side}方向杠杆失败: {str(e)}", level='error')
                        raise
                        
                self.log_trading(f"杠杆倍数已更新为: {value}x", level='info')
                
            except Exception as e:
                # 回滚杠杆设置
                self.ai_leverage_spinbox.blockSignals(True)
                self.ai_leverage_spinbox.setValue(self.ai_leverage_spinbox.value())
                self.ai_leverage_spinbox.blockSignals(False)
                raise
                
        except Exception as e:
            self.log_trading(f"更新杠杆倍数失败: {str(e)}", level='error')

    def apply_tp_sl_settings(self):
        """应用止盈止损设置"""
        try:
            tp_percent = self.tp_spinbox.value()
            sl_percent = self.sl_spinbox.value()
            
            if tp_percent <= 0 or sl_percent <= 0:
                self.show_message_signal.emit("警告", "止盈止损比例必须大于0", "warning")
                return
                
            # 获取当前价格
            symbol = self.ai_trading_symbol_combo.currentText()
            ticker = self.exchange.fetch_ticker(symbol)
            current_price = ticker['last']
            
            # 保存止盈止损设置到实例变量，供AI交易使用
            self.tp_percent = tp_percent
            self.sl_percent = sl_percent
            
            # 更新界面显示
            self.log_trading(f"已更新止盈止损设置: 止盈{tp_percent}%, 止损{sl_percent}%")
            
            self.show_message_signal.emit(
                "成功", 
                f"止盈止损设置已更新\n止盈比例: {tp_percent}%\n止损比例: {sl_percent}%", 
                "info"
            )
            
        except Exception as e:
            self.show_message_signal.emit("错误", f"更新止盈止损失败: {str(e)}", "error")

    def manual_trigger_analysis(self):
        """手动触发分析"""
        try:
            # 获取当前交易对
            symbol = self.ai_trading_symbol_combo.currentText()
            
            # 获取当前价格
            ticker = self.exchange.fetch_ticker(symbol)
            current_price = ticker['last']
            
            # 设置last_trigger_price为当前价格
            self.last_trigger_price = current_price
            
            # 触发分析
            self.log_trading("手动触发分析...")
            analysis_result = self.get_trading_signal(symbol.split('/')[0])
            
            if analysis_result:
                # 更新界面显示
                self.trend_value.setText(analysis_result['trend'])
                self.entry_price_value.setText(analysis_result['entry_price'])
                self.tp_price_value.setText(analysis_result['tp_price'])
                self.sl_price_value.setText(analysis_result['sl_price'])
                self.analysis_text.setText(analysis_result['analysis'])
                
                self.show_message_signal.emit("成功", "分析已完成", "info")
            else:
                self.show_message_signal.emit("警告", "无法获取分析结果", "warning")
                
        except Exception as e:
            self.show_message_signal.emit("错误", f"触发分析失败: {str(e)}", "error")

    def show_indicator_settings(self):
        """显示技术指标设置对话框"""
        try:
            # 创建对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("技术指标设置")
            dialog.setMinimumWidth(480)
            dialog.setStyleSheet("""
                QDialog {
                    background-color: #0B0E11;
                }
                QLabel {
                    color: #E6E8EA;
                    font-size: 14px;
                    min-height: 25px;
                }
                QDoubleSpinBox, QSpinBox {
                    padding: 5px;
                    border: 1px solid #2a2e30;
                    border-radius: 4px;
                    background-color: #1b1e22;
                    color: #E6E8EA;
                    font-size: 13px;
                    min-width: 100px;
                }
                QDoubleSpinBox:focus, QSpinBox:focus {
                    border: 1px solid #35383c;
                }
                QGroupBox {
                    font-weight: bold;
                    font-size: 14px;
                    color: #F0B90B;
                    margin-top: 1.5em;
                    padding-top: 15px;
                }
                QGroupBox::title {
                    subcontrol-origin: margin;
                    left: 10px;
                    padding: 0 5px;
                }
                QPushButton {
                    padding: 8px 15px;
                    background-color: #2EBD85;
                    color: white;
                    border: none;
                    border-radius: 4px;
                    font-size: 14px;
                    font-weight: bold;
                    min-height: 36px;
                }
                QPushButton:hover {
                    background-color: #259C6C;
                }
                QPushButton[type="cancel"] {
                    background-color: #2a2e30;
                    color: #E6E8EA;
                }
                QPushButton[type="cancel"]:hover {
                    background-color: #35383c;
                }
                QPushButton[type="preset"] {
                    background-color: #F0B90B;
                    color: #0B0E11;
                    padding: 6px 12px;
                    font-weight: bold;
                    font-size: 13px;
                    min-height: 30px;
                }
                QPushButton[type="preset"]:hover {
                    background-color: #F8D33A;
                }
                QComboBox {
                    padding: 5px;
                    border: 1px solid #2a2e30;
                    border-radius: 4px;
                    background-color: #1b1e22;
                    color: #E6E8EA;
                    font-size: 13px;
                    min-width: 100px;
                }
                QComboBox:focus {
                    border: 1px solid #35383c;
                }
                QComboBox::drop-down {
                    subcontrol-origin: padding;
                    subcontrol-position: center right;
                    width: 20px;
                    border-left-width: 1px;
                    border-left-color: #2a2e30;
                    border-left-style: solid;
                }
                QComboBox QAbstractItemView {
                    background-color: #1b1e22;
                    border: 1px solid #2a2e30;
                    selection-background-color: #2a2e30;
                }
            """)
            
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(20, 20, 20, 20)
            layout.setSpacing(15)
            
            # 加载当前设置（如果有）
            self.load_indicator_settings()
            
            # 添加时段预设选择部分
            preset_group = QGroupBox("时段预设")
            preset_layout = QVBoxLayout()
            
            # 添加说明标签
            preset_label = QLabel("选择不同时段的预设参数，快速应用于技术指标:")
            preset_label.setWordWrap(True)
            preset_layout.addWidget(preset_label)
            
            # 创建预设时段选择按钮
            preset_buttons_layout = QHBoxLayout()
            preset_buttons_layout.setSpacing(10)
            
            # 短期按钮 (5分钟/15分钟)
            short_term_btn = QPushButton("短期 (5-15分钟)")
            short_term_btn.setProperty("type", "preset")
            short_term_btn.clicked.connect(lambda: self.apply_timeframe_preset("short_term", dialog))
            
            # 中期按钮 (1小时/4小时)
            medium_term_btn = QPushButton("中期 (1-4小时)")
            medium_term_btn.setProperty("type", "preset")
            medium_term_btn.clicked.connect(lambda: self.apply_timeframe_preset("medium_term", dialog))
            
            # 长期按钮 (日线/周线)
            long_term_btn = QPushButton("长期 (日/周)")
            long_term_btn.setProperty("type", "preset")
            long_term_btn.clicked.connect(lambda: self.apply_timeframe_preset("long_term", dialog))
            
            # 添加按钮到布局
            preset_buttons_layout.addWidget(short_term_btn)
            preset_buttons_layout.addWidget(medium_term_btn)
            preset_buttons_layout.addWidget(long_term_btn)
            
            preset_layout.addLayout(preset_buttons_layout)
            
            # 添加市场类型选择
            market_type_layout = QHBoxLayout()
            market_type_layout.addWidget(QLabel("市场类型:"))
            
            self.market_type_combo = QComboBox()
            self.market_type_combo.addItems(["普通市场", "震荡市场", "强趋势市场"])
            # 创建中介函数避免直接引用dialog
            self.market_type_combo.currentIndexChanged.connect(lambda idx: self.apply_market_type_preset(dialog))
            market_type_layout.addWidget(self.market_type_combo)
            
            # 添加币种类型选择
            market_type_layout.addWidget(QLabel("币种类型:"))
            
            self.coin_type_combo = QComboBox()
            self.coin_type_combo.addItems(["主流币", "山寨币", "高波动币"])
            # 创建中介函数避免直接引用dialog
            self.coin_type_combo.currentIndexChanged.connect(lambda idx: self.apply_coin_type_preset(dialog))
            market_type_layout.addWidget(self.coin_type_combo)
            
            preset_layout.addLayout(market_type_layout)
            preset_group.setLayout(preset_layout)
            layout.addWidget(preset_group)
            
            # 创建滚动区域
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setStyleSheet("""
                QScrollArea {
                    border: none;
                    background-color: transparent;
                }
                QScrollBar:vertical {
                    background: #2A2A2A;
                    width: 10px;
                    margin: 0px 0px 0px 0px;
                }
                QScrollBar::handle:vertical {
                    background: #444444;
                    min-height: 20px;
                    border-radius: 5px;
                }
                QScrollBar::add-line:vertical,
                QScrollBar::sub-line:vertical {
                    background: none;
                }
                QScrollBar::add-page:vertical,
                QScrollBar::sub-page:vertical {
                    background: none;
                }
            """)
            
            scroll_content = QWidget()
            scroll_layout = QVBoxLayout(scroll_content)
            scroll_layout.setContentsMargins(5, 5, 5, 5)
            scroll_layout.setSpacing(15)
            
            # 添加描述样式
            desc_style = """
                QLabel[type="desc"] {
                    color: #a0a0a0;
                    font-size: 12px;
                    font-style: italic;
                    padding-left: 10px;
                    padding-right: 10px;
                }
            """
            scroll_content.setStyleSheet(desc_style)
            
            # === 趋势指标 ===
            trend_group = QGroupBox("趋势指标")
            trend_layout = QVBoxLayout()
            trend_layout.setSpacing(15)
            
            # 移动平均线设置
            ma_box = QGroupBox("移动平均线 (EMA)")
            ma_desc = QLabel("移动平均线用于判断价格趋势方向，多条均线的交叉可以反映趋势变化。短期均线上穿长期均线形成金叉做多信号，短期均线下穿长期均线形成死叉做空信号。")
            ma_desc.setProperty("type", "desc")
            ma_desc.setWordWrap(True)
            
            ma_control = QGridLayout()
            ma_control.setVerticalSpacing(10)
            
            # EMA短周期
            self.ema_short_spinbox = QSpinBox()
            self.ema_short_spinbox.setRange(5, 50)
            self.ema_short_spinbox.setValue(getattr(self, 'ema_short', 20))
            ma_control.addWidget(QLabel("EMA短周期:"), 0, 0)
            ma_control.addWidget(self.ema_short_spinbox, 0, 1)
            
            # EMA中周期
            self.ema_medium_spinbox = QSpinBox()
            self.ema_medium_spinbox.setRange(20, 100)
            self.ema_medium_spinbox.setValue(getattr(self, 'ema_medium', 50))
            ma_control.addWidget(QLabel("EMA中周期:"), 1, 0)
            ma_control.addWidget(self.ema_medium_spinbox, 1, 1)
            
            # EMA长周期
            self.ema_long_spinbox = QSpinBox()
            self.ema_long_spinbox.setRange(100, 300)
            self.ema_long_spinbox.setValue(getattr(self, 'ema_long', 200))
            ma_control.addWidget(QLabel("EMA长周期:"), 2, 0)
            ma_control.addWidget(self.ema_long_spinbox, 2, 1)
            
            ma_layout = QVBoxLayout()
            ma_layout.addWidget(ma_desc)
            ma_layout.addLayout(ma_control)
            ma_box.setLayout(ma_layout)
            trend_layout.addWidget(ma_box)
            
            # MACD指标设置
            macd_box = QGroupBox("MACD指标")
            macd_desc = QLabel("MACD (移动平均线收敛发散指标) 结合了趋势和动量分析，是一种经典的趋势确认指标。当MACD线上穿信号线形成金叉时产生买入信号；当MACD线下穿信号线形成死叉时产生卖出信号。")
            macd_desc.setProperty("type", "desc")
            macd_desc.setWordWrap(True)
            
            macd_control = QGridLayout()
            macd_control.setVerticalSpacing(10)
            
            # MACD快线周期
            self.macd_fast_spinbox = QSpinBox()
            self.macd_fast_spinbox.setRange(5, 30)
            self.macd_fast_spinbox.setValue(getattr(self, 'macd_fast', 12))
            macd_control.addWidget(QLabel("快线周期:"), 0, 0)
            macd_control.addWidget(self.macd_fast_spinbox, 0, 1)
            
            # MACD慢线周期
            self.macd_slow_spinbox = QSpinBox()
            self.macd_slow_spinbox.setRange(10, 50)
            self.macd_slow_spinbox.setValue(getattr(self, 'macd_slow', 26))
            macd_control.addWidget(QLabel("慢线周期:"), 1, 0)
            macd_control.addWidget(self.macd_slow_spinbox, 1, 1)
            
            # MACD信号线周期
            self.macd_signal_spinbox = QSpinBox()
            self.macd_signal_spinbox.setRange(5, 20)
            self.macd_signal_spinbox.setValue(getattr(self, 'macd_signal', 9))
            macd_control.addWidget(QLabel("信号线周期:"), 2, 0)
            macd_control.addWidget(self.macd_signal_spinbox, 2, 1)
            
            macd_layout = QVBoxLayout()
            macd_layout.addWidget(macd_desc)
            macd_layout.addLayout(macd_control)
            macd_box.setLayout(macd_layout)
            trend_layout.addWidget(macd_box)
            
            # ADX指标设置
            adx_box = QGroupBox("ADX指标")
            adx_desc = QLabel("ADX (平均方向指数) 用于衡量市场趋势的强度，而非方向。高ADX值表示强趋势，低ADX值表示弱趋势或盘整。适用于识别趋势市场，避免在震荡市场进行趋势交易。")
            adx_desc.setProperty("type", "desc")
            adx_desc.setWordWrap(True)
            
            adx_control = QGridLayout()
            adx_control.setVerticalSpacing(10)
            
            # ADX指标周期
            self.adx_period_spinbox = QSpinBox()
            self.adx_period_spinbox.setRange(5, 50)
            self.adx_period_spinbox.setValue(getattr(self, 'adx_period', 14))
            adx_control.addWidget(QLabel("ADX周期:"), 0, 0)
            adx_control.addWidget(self.adx_period_spinbox, 0, 1)
            
            # ADX指标阈值
            self.adx_threshold_spinbox = QSpinBox()
            self.adx_threshold_spinbox.setRange(15, 40)
            self.adx_threshold_spinbox.setValue(getattr(self, 'adx_threshold', 25))
            adx_control.addWidget(QLabel("ADX趋势阈值:"), 1, 0)
            adx_control.addWidget(self.adx_threshold_spinbox, 1, 1)
            
            adx_layout = QVBoxLayout()
            adx_layout.addWidget(adx_desc)
            adx_layout.addLayout(adx_control)
            adx_box.setLayout(adx_layout)
            trend_layout.addWidget(adx_box)
            
            trend_group.setLayout(trend_layout)
            scroll_layout.addWidget(trend_group)
            
            # === 波动指标 ===
            volatility_group = QGroupBox("波动指标")
            volatility_layout = QVBoxLayout()
            volatility_layout.setSpacing(15)
            
            # 布林带设置
            bb_box = QGroupBox("布林带 (Bollinger Bands)")
            bb_desc = QLabel("布林带通过标准差测量价格波动性，由中轨（均线）和上下轨（标准差）组成。价格触及上轨可能超买，触及下轨可能超卖。窄带表示低波动可能孕育大行情，宽带表示高波动可能即将减弱。")
            bb_desc.setProperty("type", "desc")
            bb_desc.setWordWrap(True)
            
            bb_control = QGridLayout()
            bb_control.setVerticalSpacing(10)
            
            # 布林带周期
            self.bb_period_spinbox = QSpinBox()
            self.bb_period_spinbox.setRange(5, 50)
            self.bb_period_spinbox.setValue(getattr(self, 'bb_period', 20))
            bb_control.addWidget(QLabel("周期:"), 0, 0)
            bb_control.addWidget(self.bb_period_spinbox, 0, 1)
            
            # 布林带标准差倍数
            self.bb_std_spinbox = QDoubleSpinBox()
            self.bb_std_spinbox.setRange(1.0, 4.0)
            self.bb_std_spinbox.setDecimals(1)
            self.bb_std_spinbox.setSingleStep(0.1)
            self.bb_std_spinbox.setValue(getattr(self, 'bb_std', 2.0))
            bb_control.addWidget(QLabel("标准差倍数:"), 1, 0)
            bb_control.addWidget(self.bb_std_spinbox, 1, 1)
            
            bb_layout = QVBoxLayout()
            bb_layout.addWidget(bb_desc)
            bb_layout.addLayout(bb_control)
            bb_box.setLayout(bb_layout)
            volatility_layout.addWidget(bb_box)
            
            volatility_group.setLayout(volatility_layout)
            scroll_layout.addWidget(volatility_group)
            
            # === 动量指标 ===
            momentum_group = QGroupBox("动量指标")
            momentum_layout = QVBoxLayout()
            momentum_layout.setSpacing(15)
            
            # RSI指标设置
            rsi_box = QGroupBox("RSI指标 (相对强弱指标)")
            rsi_desc = QLabel("RSI衡量价格变动的速度和幅度，用于判断市场超买超卖状态。RSI高于70通常视为超买，低于30视为超卖。还可以通过RSI的背离识别潜在的反转信号。")
            rsi_desc.setProperty("type", "desc")
            rsi_desc.setWordWrap(True)
            
            rsi_control = QGridLayout()
            rsi_control.setVerticalSpacing(10)
            
            # RSI周期
            self.rsi_period_spinbox = QSpinBox()
            self.rsi_period_spinbox.setRange(5, 50)
            self.rsi_period_spinbox.setValue(getattr(self, 'rsi_period', 14))
            rsi_control.addWidget(QLabel("RSI周期:"), 0, 0)
            rsi_control.addWidget(self.rsi_period_spinbox, 0, 1)
            
            # RSI超买阈值
            self.rsi_overbought_spinbox = QSpinBox()
            self.rsi_overbought_spinbox.setRange(60, 90)
            self.rsi_overbought_spinbox.setValue(getattr(self, 'rsi_overbought', 70))
            rsi_control.addWidget(QLabel("超买阈值:"), 1, 0)
            rsi_control.addWidget(self.rsi_overbought_spinbox, 1, 1)
            
            # RSI超卖阈值
            self.rsi_oversold_spinbox = QSpinBox()
            self.rsi_oversold_spinbox.setRange(10, 40)
            self.rsi_oversold_spinbox.setValue(getattr(self, 'rsi_oversold', 30))
            rsi_control.addWidget(QLabel("超卖阈值:"), 2, 0)
            rsi_control.addWidget(self.rsi_oversold_spinbox, 2, 1)
            
            rsi_layout = QVBoxLayout()
            rsi_layout.addWidget(rsi_desc)
            rsi_layout.addLayout(rsi_control)
            rsi_box.setLayout(rsi_layout)
            momentum_layout.addWidget(rsi_box)
            
            # 随机指标设置
            stoch_box = QGroupBox("KDJ随机指标")
            stoch_desc = QLabel("随机指标比较收盘价与一段时间内的价格区间，用于识别超买超卖状态和潜在反转点。指标高于80时可能超买，低于20时可能超卖。K线与D线的交叉也能产生交易信号。")
            stoch_desc.setProperty("type", "desc")
            stoch_desc.setWordWrap(True)
            
            stoch_control = QGridLayout()
            stoch_control.setVerticalSpacing(10)
            
            # 随机指标超买阈值
            self.stoch_overbought_spinbox = QSpinBox()
            self.stoch_overbought_spinbox.setRange(70, 90)
            self.stoch_overbought_spinbox.setValue(getattr(self, 'stoch_overbought', 80))
            stoch_control.addWidget(QLabel("超买阈值:"), 0, 0)
            stoch_control.addWidget(self.stoch_overbought_spinbox, 0, 1)
            
            # 随机指标超卖阈值
            self.stoch_oversold_spinbox = QSpinBox()
            self.stoch_oversold_spinbox.setRange(10, 30)
            self.stoch_oversold_spinbox.setValue(getattr(self, 'stoch_oversold', 20))
            stoch_control.addWidget(QLabel("超卖阈值:"), 1, 0)
            stoch_control.addWidget(self.stoch_oversold_spinbox, 1, 1)
            
            stoch_layout = QVBoxLayout()
            stoch_layout.addWidget(stoch_desc)
            stoch_layout.addLayout(stoch_control)
            stoch_box.setLayout(stoch_layout)
            momentum_layout.addWidget(stoch_box)
            
            # CCI指标设置
            cci_box = QGroupBox("CCI指标 (商品通道指数)")
            cci_desc = QLabel("CCI衡量价格偏离其移动平均线的程度，用于识别价格动量变化和超买超卖状态。CCI高于+100可能超买，低于-100可能超卖。适合震荡市场，可以捕捉短期反转机会。")
            cci_desc.setProperty("type", "desc")
            cci_desc.setWordWrap(True)
            
            cci_control = QGridLayout()
            cci_control.setVerticalSpacing(10)
            
            # CCI指标超买阈值
            self.cci_overbought_spinbox = QSpinBox()
            self.cci_overbought_spinbox.setRange(80, 200)
            self.cci_overbought_spinbox.setValue(getattr(self, 'cci_overbought', 100))
            cci_control.addWidget(QLabel("超买阈值:"), 0, 0)
            cci_control.addWidget(self.cci_overbought_spinbox, 0, 1)
            
            # CCI指标超卖阈值
            self.cci_oversold_spinbox = QSpinBox()
            self.cci_oversold_spinbox.setRange(-200, -80)
            self.cci_oversold_spinbox.setValue(getattr(self, 'cci_oversold', -100))
            cci_control.addWidget(QLabel("超卖阈值:"), 1, 0)
            cci_control.addWidget(self.cci_oversold_spinbox, 1, 1)
            
            cci_layout = QVBoxLayout()
            cci_layout.addWidget(cci_desc)
            cci_layout.addLayout(cci_control)
            cci_box.setLayout(cci_layout)
            momentum_layout.addWidget(cci_box)
            
            # MFI指标设置
            mfi_box = QGroupBox("MFI指标 (资金流向指标)")
            mfi_desc = QLabel("MFI结合了价格和交易量，衡量资金流入和流出的压力。MFI高于80表示可能超买，低于20表示可能超卖。与价格的背离可以提供强有力的反转信号，特别适合识别市场顶部和底部。")
            mfi_desc.setProperty("type", "desc")
            mfi_desc.setWordWrap(True)
            
            mfi_control = QGridLayout()
            mfi_control.setVerticalSpacing(10)
            
            # MFI指标超买阈值
            self.mfi_overbought_spinbox = QSpinBox()
            self.mfi_overbought_spinbox.setRange(70, 90)
            self.mfi_overbought_spinbox.setValue(getattr(self, 'mfi_overbought', 80))
            mfi_control.addWidget(QLabel("超买阈值:"), 0, 0)
            mfi_control.addWidget(self.mfi_overbought_spinbox, 0, 1)
            
            # MFI指标超卖阈值
            self.mfi_oversold_spinbox = QSpinBox()
            self.mfi_oversold_spinbox.setRange(10, 30)
            self.mfi_oversold_spinbox.setValue(getattr(self, 'mfi_oversold', 20))
            mfi_control.addWidget(QLabel("超卖阈值:"), 1, 0)
            mfi_control.addWidget(self.mfi_oversold_spinbox, 1, 1)
            
            mfi_layout = QVBoxLayout()
            mfi_layout.addWidget(mfi_desc)
            mfi_layout.addLayout(mfi_control)
            mfi_box.setLayout(mfi_layout)
            momentum_layout.addWidget(mfi_box)
            
            momentum_group.setLayout(momentum_layout)
            scroll_layout.addWidget(momentum_group)
            
            # 设置内容到滚动区域
            scroll_area.setWidget(scroll_content)
            layout.addWidget(scroll_area, 1)
            
            # 按钮区域
            button_layout = QHBoxLayout()
            button_layout.setSpacing(15)
            
            save_button = QPushButton("保存设置")
            save_button.clicked.connect(lambda: self.save_indicator_settings(dialog))
            
            reset_button = QPushButton("恢复默认")
            reset_button.setProperty("type", "cancel")
            reset_button.clicked.connect(self.reset_indicator_settings)
            
            cancel_button = QPushButton("取消")
            cancel_button.setProperty("type", "cancel")
            cancel_button.clicked.connect(dialog.reject)
            
            button_layout.addWidget(cancel_button)
            button_layout.addWidget(reset_button)
            button_layout.addWidget(save_button)
            
            layout.addLayout(button_layout)
            
            # 显示对话框
            dialog.exec()
            
        except Exception as e:
            self.show_message_signal.emit("错误", f"显示技术指标设置失败: {str(e)}", "error")
    
    def load_indicator_settings(self):
        """从配置文件加载指标设置"""
        try:
            config_file = "indicator_settings.json"
            if os.path.exists(config_file):
                with open(config_file, "r") as f:
                    settings = json.load(f)
                
                # 加载RSI设置
                self.rsi_period = settings.get("rsi_period", 14)
                self.rsi_overbought = settings.get("rsi_overbought", 70)
                self.rsi_oversold = settings.get("rsi_oversold", 30)
                
                # 加载MACD设置
                self.macd_fast = settings.get("macd_fast", 12)
                self.macd_slow = settings.get("macd_slow", 26)
                self.macd_signal = settings.get("macd_signal", 9)
                
                # 加载移动平均线设置
                self.ema_short = settings.get("ema_short", 20)
                self.ema_medium = settings.get("ema_medium", 50)
                self.ema_long = settings.get("ema_long", 200)
                
                # 加载布林带设置
                self.bb_period = settings.get("bb_period", 20)
                self.bb_std = settings.get("bb_std", 2.0)
                
                # 加载其他指标设置
                self.adx_period = settings.get("adx_period", 14) # 新增ADX周期
                self.adx_threshold = settings.get("adx_threshold", 25)
                self.stoch_overbought = settings.get("stoch_overbought", 80)
                self.stoch_oversold = settings.get("stoch_oversold", 20)
                self.cci_overbought = settings.get("cci_overbought", 100)
                self.cci_oversold = settings.get("cci_oversold", -100)
                self.mfi_overbought = settings.get("mfi_overbought", 80)
                self.mfi_oversold = settings.get("mfi_oversold", 20)
                
                # 加载预设信息
                self.timeframe_preset = settings.get("timeframe_preset", "medium_term")
                self.market_type = settings.get("market_type", "普通市场")
                self.coin_type = settings.get("coin_type", "主流币")
                
                # 如果UI已初始化，更新下拉菜单选择
                if hasattr(self, 'market_type_combo'):
                    index = self.market_type_combo.findText(self.market_type)
                    if index >= 0:
                        self.market_type_combo.setCurrentIndex(index)
                        
                if hasattr(self, 'coin_type_combo'):
                    index = self.coin_type_combo.findText(self.coin_type)
                    if index >= 0:
                        self.coin_type_combo.setCurrentIndex(index)
                
                self.log_trading(f"已加载技术指标设置 (时段预设: {self.timeframe_preset}, 市场类型: {self.market_type}, 币种类型: {self.coin_type})", level='info')
            else:
                # 设置默认值
                self.reset_indicator_settings()
        except Exception as e:
            self.show_message_signal.emit("警告", f"加载技术指标设置失败，将使用默认值: {str(e)}", "warning")
            self.reset_indicator_settings()
    
    def save_indicator_settings(self, dialog):
        """保存指标设置到配置文件"""
        try:
            # 收集设置
            settings = {
                # RSI设置
                "rsi_period": self.rsi_period_spinbox.value(),
                "rsi_overbought": self.rsi_overbought_spinbox.value(),
                "rsi_oversold": self.rsi_oversold_spinbox.value(),
                
                # MACD设置
                "macd_fast": self.macd_fast_spinbox.value(),
                "macd_slow": self.macd_slow_spinbox.value(),
                "macd_signal": self.macd_signal_spinbox.value(),
                
                # 移动平均线设置
                "ema_short": self.ema_short_spinbox.value(),
                "ema_medium": self.ema_medium_spinbox.value(),
                "ema_long": self.ema_long_spinbox.value(),
                
                # 布林带设置
                "bb_period": self.bb_period_spinbox.value(),
                "bb_std": self.bb_std_spinbox.value(),
                
                # 其他指标设置
                "adx_period": self.adx_period_spinbox.value(), # 新增ADX周期
                "adx_threshold": self.adx_threshold_spinbox.value(),
                "stoch_overbought": self.stoch_overbought_spinbox.value(),
                "stoch_oversold": self.stoch_oversold_spinbox.value(),
                "cci_overbought": self.cci_overbought_spinbox.value(),
                "cci_oversold": self.cci_oversold_spinbox.value(),
                "mfi_overbought": self.mfi_overbought_spinbox.value(),
                "mfi_oversold": self.mfi_oversold_spinbox.value(),
                
                # 保存当前选择的预设
                "market_type": self.market_type_combo.currentText() if hasattr(self, 'market_type_combo') else "普通市场",
                "coin_type": self.coin_type_combo.currentText() if hasattr(self, 'coin_type_combo') else "主流币"
            }
            
            # 判断当前参数最接近哪个时段预设
            # 创建预设参数字典，用于比较
            presets = {
                "short_term": {
                    "rsi_period": 8,
                    "macd_fast": 6,
                    "ema_short": 10,
                    "bb_period": 10
                },
                "medium_term": {
                    "rsi_period": 14,
                    "macd_fast": 12,
                    "ema_short": 20,
                    "bb_period": 20
                },
                "long_term": {
                    "rsi_period": 21,
                    "macd_fast": 15,
                    "ema_short": 30,
                    "bb_period": 30
                }
            }
            
            # 计算当前设置与各预设的差异
            differences = {}
            current_values = {
                "rsi_period": self.rsi_period_spinbox.value(),
                "macd_fast": self.macd_fast_spinbox.value(),
                "ema_short": self.ema_short_spinbox.value(),
                "bb_period": self.bb_period_spinbox.value()
            }
            
            for preset_name, preset_values in presets.items():
                diff_sum = 0
                for key, value in preset_values.items():
                    diff_sum += abs(current_values[key] - value)
                differences[preset_name] = diff_sum
            
            # 找出差异最小的预设
            closest_preset = min(differences, key=differences.get)
            settings["timeframe_preset"] = closest_preset
            
            # 更新类属性
            for key, value in settings.items():
                setattr(self, key, value)
            
            # 保存到文件
            config_file = "indicator_settings.json"
            with open(config_file, "w") as f:
                json.dump(settings, f, indent=4)
            
            self.show_message_signal.emit("成功", "技术指标设置已保存", "info")
            dialog.accept()
        except Exception as e:
            self.show_message_signal.emit("错误", f"保存技术指标设置失败: {str(e)}", "error")
    
    def reset_indicator_settings(self):
        """重置为默认设置"""
        try:
            # RSI默认设置
            self.rsi_period = 14
            self.rsi_overbought = 70
            self.rsi_oversold = 30
            
            # MACD默认设置
            self.macd_fast = 12
            self.macd_slow = 26
            self.macd_signal = 9
            
            # 移动平均线默认设置
            self.ema_short = 20
            self.ema_medium = 50
            self.ema_long = 200
            
            # 布林带默认设置
            self.bb_period = 20
            self.bb_std = 2.0
            
            # 其他指标默认设置
            self.adx_period = 14 # 新增ADX周期
            self.adx_threshold = 25
            self.stoch_overbought = 80
            self.stoch_oversold = 20
            self.cci_overbought = 100
            self.cci_oversold = -100
            self.mfi_overbought = 80
            self.mfi_oversold = 20
            
            # 重置预设信息
            self.timeframe_preset = "medium_term"
            self.market_type = "普通市场"
            self.coin_type = "主流币"
            
            # 如果对话框已打开，更新控件值
            if hasattr(self, 'rsi_period_spinbox'):
                self.rsi_period_spinbox.setValue(self.rsi_period)
                self.rsi_overbought_spinbox.setValue(self.rsi_overbought)
                self.rsi_oversold_spinbox.setValue(self.rsi_oversold)
                
                self.macd_fast_spinbox.setValue(self.macd_fast)
                self.macd_slow_spinbox.setValue(self.macd_slow)
                self.macd_signal_spinbox.setValue(self.macd_signal)
                
                self.ema_short_spinbox.setValue(self.ema_short)
                self.ema_medium_spinbox.setValue(self.ema_medium)
                self.ema_long_spinbox.setValue(self.ema_long)
                
                self.bb_period_spinbox.setValue(self.bb_period)
                self.bb_std_spinbox.setValue(self.bb_std)
                
                self.adx_period_spinbox.setValue(self.adx_period) # 新增ADX周期
                self.adx_threshold_spinbox.setValue(self.adx_threshold)
                self.stoch_overbought_spinbox.setValue(self.stoch_overbought)
                self.stoch_oversold_spinbox.setValue(self.stoch_oversold)
                self.cci_overbought_spinbox.setValue(self.cci_overbought)
                self.cci_oversold_spinbox.setValue(self.cci_oversold)
                self.mfi_overbought_spinbox.setValue(self.mfi_overbought)
                self.mfi_oversold_spinbox.setValue(self.mfi_oversold)
                
                # 更新下拉选择框
                if hasattr(self, 'market_type_combo'):
                    index = self.market_type_combo.findText(self.market_type)
                    if index >= 0:
                        self.market_type_combo.setCurrentIndex(index)
                        
                if hasattr(self, 'coin_type_combo'):
                    index = self.coin_type_combo.findText(self.coin_type)
                    if index >= 0:
                        self.coin_type_combo.setCurrentIndex(index)
            
            self.show_message_signal.emit("提示", "已重置为默认技术指标设置", "info")
        except Exception as e:
            self.show_message_signal.emit("错误", f"重置技术指标设置失败: {str(e)}", "error")

    def refresh_chart_data(self):
        """刷新K线图数据"""
        try:
            # 清除缓存，强制重新获取数据
            symbol = self.symbol_combo.currentText()
            base_quote = symbol.split('/')
            base, quote = base_quote[0], base_quote[1]
            okx_symbol = f"{base}-{quote}-SWAP"
            
            timeframe = '15m'
            if hasattr(self.chart_widget, 'timeframe_combo'):
                selected_tf = self.chart_widget.timeframe_combo.currentText()
                if selected_tf == '5分钟':
                    timeframe = '5m'
                elif selected_tf == '15分钟':
                    timeframe = '15m'
                elif selected_tf == '1小时':
                    timeframe = '1h'
                elif selected_tf == '4小时':
                    timeframe = '4h'
                elif selected_tf == '1天':
                    timeframe = '1d'
            
            # 从缓存中移除该时间周期的数据，强制刷新
            cache_key = f"{okx_symbol}_{timeframe}"
            if cache_key in self.data_cache:
                del self.data_cache[cache_key]
            
            self.log_trading(f"刷新K线图数据: {okx_symbol}, 时间周期: {timeframe}", level='info')
            # 触发数据更新，使用现有的更新机制
            self.update_market_data()
        except Exception as e:
            self.log_trading(f"刷新K线图数据失败: {str(e)}", level='error')

    def apply_timeframe_preset(self, timeframe, dialog):
        """应用特定时段的预设参数"""
        try:
            # 短期交易设置 (5-15分钟)
            if timeframe == "short_term":
                # RSI设置
                self.rsi_period_spinbox.setValue(8)
                self.rsi_overbought_spinbox.setValue(75)
                self.rsi_oversold_spinbox.setValue(25)
                
                # MACD设置
                self.macd_fast_spinbox.setValue(6)
                self.macd_slow_spinbox.setValue(13)
                self.macd_signal_spinbox.setValue(5)
                
                # 移动平均线设置
                self.ema_short_spinbox.setValue(10)
                self.ema_medium_spinbox.setValue(25)
                self.ema_long_spinbox.setValue(100)
                
                # 布林带设置
                self.bb_period_spinbox.setValue(10)
                self.bb_std_spinbox.setValue(2.2)
                
                # 其他指标设置
                self.adx_threshold_spinbox.setValue(20)
                self.stoch_overbought_spinbox.setValue(85)
                self.stoch_oversold_spinbox.setValue(15)
                self.cci_overbought_spinbox.setValue(120)
                self.cci_oversold_spinbox.setValue(-120)
                self.mfi_overbought_spinbox.setValue(85)
                self.mfi_oversold_spinbox.setValue(15)
                
                self.show_message_signal.emit("提示", "已应用短期交易 (5-15分钟) 参数", "info")
            
            # 中期交易设置 (1-4小时)
            elif timeframe == "medium_term":
                # RSI设置
                self.rsi_period_spinbox.setValue(14)
                self.rsi_overbought_spinbox.setValue(70)
                self.rsi_oversold_spinbox.setValue(30)
                
                # MACD设置
                self.macd_fast_spinbox.setValue(12)
                self.macd_slow_spinbox.setValue(26)
                self.macd_signal_spinbox.setValue(9)
                
                # 移动平均线设置
                self.ema_short_spinbox.setValue(20)
                self.ema_medium_spinbox.setValue(50)
                self.ema_long_spinbox.setValue(200)
                
                # 布林带设置
                self.bb_period_spinbox.setValue(20)
                self.bb_std_spinbox.setValue(2.0)
                
                # 其他指标设置
                self.adx_threshold_spinbox.setValue(25)
                self.stoch_overbought_spinbox.setValue(80)
                self.stoch_oversold_spinbox.setValue(20)
                self.cci_overbought_spinbox.setValue(100)
                self.cci_oversold_spinbox.setValue(-100)
                self.mfi_overbought_spinbox.setValue(80)
                self.mfi_oversold_spinbox.setValue(20)
                
                self.show_message_signal.emit("提示", "已应用中期交易 (1-4小时) 参数", "info")
            
            # 长期交易设置 (日/周线)
            elif timeframe == "long_term":
                # RSI设置
                self.rsi_period_spinbox.setValue(21)
                self.rsi_overbought_spinbox.setValue(65)
                self.rsi_oversold_spinbox.setValue(35)
                
                # MACD设置
                self.macd_fast_spinbox.setValue(15)
                self.macd_slow_spinbox.setValue(30)
                self.macd_signal_spinbox.setValue(12)
                
                # 移动平均线设置
                self.ema_short_spinbox.setValue(30)
                self.ema_medium_spinbox.setValue(60)
                self.ema_long_spinbox.setValue(200)
                
                # 布林带设置
                self.bb_period_spinbox.setValue(30)
                self.bb_std_spinbox.setValue(1.8)
                
                # 其他指标设置
                self.adx_threshold_spinbox.setValue(30)
                self.stoch_overbought_spinbox.setValue(75)
                self.stoch_oversold_spinbox.setValue(25)
                self.cci_overbought_spinbox.setValue(80)
                self.cci_oversold_spinbox.setValue(-80)
                self.mfi_overbought_spinbox.setValue(75)
                self.mfi_oversold_spinbox.setValue(25)
                
                self.show_message_signal.emit("提示", "已应用长期交易 (日/周) 参数", "info")
                
        except Exception as e:
            self.show_message_signal.emit("错误", f"应用时段预设失败: {str(e)}", "error")

    def apply_market_type_preset(self, dialog):
        """应用市场类型预设"""
        try:
            market_type = self.market_type_combo.currentText()
            
            # 震荡市场参数
            if market_type == "震荡市场":
                # 缩小RSI区间
                self.rsi_overbought_spinbox.setValue(65)
                self.rsi_oversold_spinbox.setValue(35)
                
                # 缩短布林带周期
                self.bb_period_spinbox.setValue(15)
                
                # 调整随机指标
                self.stoch_overbought_spinbox.setValue(75)
                self.stoch_oversold_spinbox.setValue(25)
                
                self.show_message_signal.emit("提示", "已应用震荡市场参数", "info")
                
            # 强趋势市场参数
            elif market_type == "强趋势市场":
                # 放宽RSI区间
                self.rsi_overbought_spinbox.setValue(80)
                self.rsi_oversold_spinbox.setValue(20)
                
                # 增加布林带标准差
                self.bb_std_spinbox.setValue(2.5)
                
                # 调低ADX阈值
                self.adx_threshold_spinbox.setValue(20)
                
                self.show_message_signal.emit("提示", "已应用强趋势市场参数", "info")
                
            # 普通市场 - 使用默认参数
            else:
                # 可以选择不做任何改变，或者加载默认参数
                pass
                
        except Exception as e:
            self.show_message_signal.emit("错误", f"应用市场类型预设失败: {str(e)}", "error")

    def apply_coin_type_preset(self, dialog):
        """应用币种类型预设"""
        try:
            coin_type = self.coin_type_combo.currentText()
            
            # 主流币参数 (BTC/ETH等)
            if coin_type == "主流币":
                # MACD设置 - 适合稳定趋势
                self.macd_fast_spinbox.setValue(10)
                self.macd_slow_spinbox.setValue(21)
                self.macd_signal_spinbox.setValue(8)
                
                # ADX阈值降低 - 主流币趋势更容易形成
                self.adx_threshold_spinbox.setValue(20)
                
                self.show_message_signal.emit("提示", "已应用主流币参数", "info")
                
            # 山寨币参数
            elif coin_type == "山寨币":
                # 默认参数适用
                
                self.show_message_signal.emit("提示", "已应用山寨币参数", "info")
                
            # 高波动币参数 (迷因币等)
            elif coin_type == "高波动币":
                # RSI设置 - 更严格的超买超卖条件
                self.rsi_overbought_spinbox.setValue(75)
                self.rsi_oversold_spinbox.setValue(25)
                
                # 布林带标准差增加 - 适应更大波动
                self.bb_std_spinbox.setValue(2.5)
                
                # 随机指标范围扩大 - 适应更大波动
                self.stoch_overbought_spinbox.setValue(85)
                self.stoch_oversold_spinbox.setValue(15)
                
                # CCI范围扩大
                self.cci_overbought_spinbox.setValue(150)
                self.cci_oversold_spinbox.setValue(-150)
                
                # MFI范围扩大
                self.mfi_overbought_spinbox.setValue(85)
                self.mfi_oversold_spinbox.setValue(15)
                
                self.show_message_signal.emit("提示", "已应用高波动币参数", "info")
                
        except Exception as e:
            self.show_message_signal.emit("错误", f"应用币种类型预设失败: {str(e)}", "error")

    def check_network_status(self):
        """检查网络状态"""
        if not hasattr(self, 'network_thread') or not self.network_thread.is_alive():
            self.network_thread = threading.Thread(target=self._check_network_status_thread, daemon=True)
            self.network_thread.start()

    def _check_network_status_thread(self):
        """在后台线程中检查网络状态"""
        check_results = {}
        
        # 检查交易所API连接
        try:
            for attempt in range(3):  # 最多尝试3次
                try:
                    status = self.exchange.fetch_status()
                    if status.get('status') == 'ok':
                        check_results["exchange"] = True
                        break
                    else:
                        time.sleep(1)
                except Exception:
                    if attempt < 2:  # 如果不是最后一次尝试
                        time.sleep(1)
                    else:
                        check_results["exchange"] = False
            
            if "exchange" not in check_results:
                check_results["exchange"] = False
                
        except Exception:
            check_results["exchange"] = False
            
        # 检查News API连接
        if self.news_api_key:
            try:
                for attempt in range(3):  # 最多尝试3次
                    try:
                        url = 'https://newsapi.org/v2/everything'
                        params = {
                            'q': 'bitcoin',
                            'pageSize': 1,
                            'apiKey': self.news_api_key
                        }
                        response = requests.get(url, params=params, timeout=5)
                        if response.status_code == 200:
                            check_results["news_api"] = True
                            break
                        else:
                            time.sleep(1)
                    except Exception:
                        if attempt < 2:  # 如果不是最后一次尝试
                            time.sleep(1)
                        else:
                            check_results["news_api"] = False
                
                if "news_api" not in check_results:
                    check_results["news_api"] = False
            except Exception:
                check_results["news_api"] = False
        else:
            check_results["news_api"] = False
            
        # 检查DeepSeek AI API连接
        if self.deepseek_api_key:
            try:
                for attempt in range(3):  # 最多尝试3次
                    try:
                        headers = {
                            'Authorization': f'Bearer {self.deepseek_api_key}',
                            'Content-Type': 'application/json'
                        }
                        api_url = "https://api.deepseek.com/v1/chat/completions"
                        api_data = {
                            "model": "deepseek-chat",
                            "messages": [{"role": "user", "content": "Hi"}],
                            "max_tokens": 5
                        }
                        response = requests.post(api_url, json=api_data, headers=headers, timeout=5)
                        if response.status_code == 200:
                            check_results["ai_api"] = True
                            break
                        else:
                            time.sleep(1)
                    except Exception:
                        if attempt < 2:  # 如果不是最后一次尝试
                            time.sleep(1)
                        else:
                            check_results["ai_api"] = False
                
                if "ai_api" not in check_results:
                    check_results["ai_api"] = False
            except Exception:
                check_results["ai_api"] = False
        else:
            check_results["ai_api"] = False
        
        # 更新所有状态
        for api_name, is_connected in check_results.items():
            self.network_status_signal.emit(api_name, is_connected)
            

    def update_network_status(self, api_name, is_connected):
        """更新网络状态指示器"""
        if api_name == "exchange":
            status_text = "交易所连接: "
            status_label = self.exchange_status
        elif api_name == "news_api":
            status_text = "News API: "
            status_label = self.news_api_status
        elif api_name == "ai_api":
            status_text = "AI API: "
            status_label = self.ai_api_status
        else:
            return
            
        if is_connected:
            status_text += "已连接"
            status_label.setStyleSheet("color: #2EBD85;")  # 绿色表示连接成功
        else:
            status_text += "未连接"
            status_label.setStyleSheet("color: #F23645;")  # 红色表示连接失败
            
        status_label.setText(status_text)
        
        # 添加时间戳到状态栏提示
        timestamp = datetime.now().strftime('%H:%M:%S')
        status_label.setToolTip(f"上次检查: {timestamp}")

    def on_ai_symbol_changed(self, symbol):
        """AI交易标签页中交易对改变时的处理"""
        try:
            # 更新AI交易价格
            self.update_ai_trading_price()
            
            # 同步更新主界面的交易对选择
            if symbol != self.symbol_combo.currentText():
                self.symbol_combo.blockSignals(True)  # 阻止触发信号
                self.symbol_combo.setCurrentText(symbol)  # 设置相同的交易对
                self.symbol_combo.blockSignals(False)  # 恢复信号处理
                
                # 更新K线图
                try:
                    ohlcv = self.exchange.fetch_ohlcv(symbol, '1m', limit=100)
                    self.chart_widget.update_data(ohlcv)
                except Exception as e:
                    self.log_trading(f"K线图更新失败: {str(e)}", level='error')
            
            # 同步更新新闻分析标签页中的交易对选择
            if hasattr(self, 'news_symbol_combo') and symbol != self.news_symbol_combo.currentText():
                self.news_symbol_combo.blockSignals(True)  # 阻止触发信号
                self.news_symbol_combo.setCurrentText(symbol)  # 设置相同的交易对
                self.news_symbol_combo.blockSignals(False)  # 恢复信号处理
        except Exception as e:
            self.log_trading(f"AI交易对变更处理失败: {str(e)}", level='error')

    def on_news_symbol_changed(self, symbol):
        """新闻分析标签页中交易对改变时的处理"""
        try:
            # 同步更新主界面的交易对选择
            if symbol != self.symbol_combo.currentText():
                self.symbol_combo.blockSignals(True)  # 阻止触发信号
                self.symbol_combo.setCurrentText(symbol)  # 设置相同的交易对
                self.symbol_combo.blockSignals(False)  # 恢复信号处理
                
                # 更新K线图
                try:
                    ohlcv = self.exchange.fetch_ohlcv(symbol, '1m', limit=100)
                    self.chart_widget.update_data(ohlcv)
                except Exception as e:
                    self.log_trading(f"K线图更新失败: {str(e)}", level='error')
            
            # 同步更新AI交易标签页中的交易对选择
            if hasattr(self, 'ai_trading_symbol_combo') and symbol != self.ai_trading_symbol_combo.currentText():
                self.ai_trading_symbol_combo.blockSignals(True)  # 阻止触发信号
                self.ai_trading_symbol_combo.setCurrentText(symbol)  # 设置相同的交易对
                self.ai_trading_symbol_combo.blockSignals(False)  # 恢复信号处理
                
                # 更新AI交易界面的实时价格
                self.update_ai_trading_price()
        except Exception as e:
            self.log_trading(f"新闻分析交易对变更处理失败: {str(e)}", level='error')

class ThreadSafeTextEdit(QTextEdit):
    append = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.append.connect(self._append)
    
    def appendHtml(self, html):
        self.append.emit(html)
    
    def _append(self, html):
        # 在主线程执行UI更新
        super().append(html)

class Worker(QRunnable):
    def __init__(self, fn, *args, **kwargs):
        super().__init__()
        self.fn = fn
        self.args = args
        self.kwargs = kwargs
        self.signals = WorkerSignals()
        
    def run(self):
        try:
            result = self.fn(*self.args, **self.kwargs)
            self.signals.result.emit(result)
        except Exception as e:
            self.signals.error.emit(e)
        finally:
            self.signals.finished.emit()

class WorkerSignals(QObject):
    result = pyqtSignal(object)
    error = pyqtSignal(Exception)
    finished = pyqtSignal()

if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec()) 