#!/usr/bin/env python3
"""
OKX 量化交易机器人启动脚本
快速启动 OKX 量化交易机器人
"""

import sys
import os
import subprocess

def check_requirements():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查 Python 版本
    if sys.version_info < (3, 8):
        print("❌ Python 版本过低，需要 Python 3.8+")
        return False
    
    print(f"✅ Python 版本: {sys.version}")
    
    # 检查必要的库
    required_packages = [
        'PyQt6', 'ccxt', 'pandas', 'numpy', 'talib', 
        'requests', 'python-dotenv'
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace('-', '_').lower())
            print(f"✅ {package}: 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}: 未安装")
    
    if missing_packages:
        print(f"\n📦 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    return True

def check_okx_support():
    """检查 OKX 支持"""
    print("\n🏢 检查 OKX 支持...")
    
    try:
        import ccxt
        if 'okx' in ccxt.exchanges:
            print("✅ CCXT 支持 OKX 交易所")
            
            # 测试创建 OKX 实例
            exchange = ccxt.okx()
            print(f"✅ OKX 实例创建成功 (版本: {exchange.version})")
            return True
        else:
            print("❌ CCXT 不支持 OKX 交易所")
            return False
    except Exception as e:
        print(f"❌ OKX 支持检查失败: {e}")
        return False

def check_config():
    """检查配置文件"""
    print("\n⚙️ 检查配置文件...")
    
    # 检查 .env.example
    if os.path.exists('.env.example'):
        print("✅ .env.example 存在")
    else:
        print("⚠️ .env.example 不存在")
    
    # 检查 .env
    if os.path.exists('.env'):
        print("✅ .env 配置文件存在")
        
        from dotenv import load_dotenv
        load_dotenv()
        
        # 检查 OKX API 配置
        okx_keys = ['OKX_API_KEY', 'OKX_SECRET_KEY', 'OKX_PASSPHRASE']
        configured_keys = []
        for key in okx_keys:
            if os.getenv(key):
                configured_keys.append(key)
                print(f"✅ {key}: 已配置")
            else:
                print(f"⚠️ {key}: 未配置")
        
        if len(configured_keys) == 3:
            print("✅ OKX API 配置完整")
            return True
        else:
            print("⚠️ OKX API 配置不完整，可以在登录界面手动输入")
            return False
    else:
        print("⚠️ .env 配置文件不存在，可以在登录界面手动输入")
        return False

def start_bot():
    """启动机器人"""
    print("\n🚀 启动 OKX 量化交易机器人...")
    
    try:
        # 启动登录器
        subprocess.run([sys.executable, 'login.py'], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ 启动失败: {e}")
        return False
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断")
        return False
    except Exception as e:
        print(f"❌ 启动异常: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 OKX 量化交易机器人启动器")
    print("=" * 60)
    
    # 检查运行环境
    if not check_requirements():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        return 1
    
    # 检查 OKX 支持
    if not check_okx_support():
        print("\n❌ OKX 支持检查失败，请检查 ccxt 库安装")
        return 1
    
    # 检查配置
    check_config()
    
    # 启动提示
    print("\n" + "=" * 60)
    print("📋 启动说明:")
    print("1. 如果没有配置 .env 文件，请在登录界面手动输入 API 密钥")
    print("2. OKX API 需要三个参数: API Key, Secret Key, Passphrase")
    print("3. 确保 API 密钥有足够的交易权限")
    print("4. 建议先在 OKX 模拟环境测试")
    print("=" * 60)
    
    input("\n按 Enter 键启动机器人...")
    
    # 启动机器人
    if start_bot():
        print("✅ 机器人已正常退出")
        return 0
    else:
        print("❌ 机器人启动失败")
        return 1

if __name__ == "__main__":
    sys.exit(main())
