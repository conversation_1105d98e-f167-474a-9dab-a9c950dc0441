#!/usr/bin/env python3
"""
OKX 登录器集成测试脚本
测试登录器和主窗口的集成
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_login_window():
    """测试登录窗口"""
    print("测试 OKX 登录窗口...")
    
    try:
        from login import LoginWindow
        
        app = QApplication(sys.argv)
        
        # 创建登录窗口
        login_window = LoginWindow()
        
        # 检查窗口属性
        print(f"窗口标题: {login_window.windowTitle()}")
        print(f"窗口大小: {login_window.size().width()}x{login_window.size().height()}")
        
        # 检查输入字段
        print(f"API Key 输入框存在: {hasattr(login_window, 'api_key_input')}")
        print(f"Secret Key 输入框存在: {hasattr(login_window, 'secret_key_input')}")
        print(f"Passphrase 输入框存在: {hasattr(login_window, 'passphrase_input')}")
        
        # 检查验证状态
        print(f"API Key 验证状态: {login_window.api_key_valid}")
        print(f"Secret Key 验证状态: {login_window.secret_key_valid}")
        print(f"Passphrase 验证状态: {login_window.passphrase_valid}")
        
        # 测试字段验证
        print("\n测试字段验证...")
        login_window.api_key_input.setText("test_api_key_1234567890")
        login_window.validate_field("api_key")
        print(f"API Key 验证后状态: {login_window.api_key_valid}")
        
        login_window.secret_key_input.setText("test_secret_key_1234567890")
        login_window.validate_field("secret_key")
        print(f"Secret Key 验证后状态: {login_window.secret_key_valid}")
        
        login_window.passphrase_input.setText("test_passphrase")
        login_window.validate_field("passphrase")
        print(f"Passphrase 验证后状态: {login_window.passphrase_valid}")
        
        # 检查登录按钮状态
        print(f"登录按钮启用状态: {login_window.login_button.isEnabled()}")
        
        # 显示窗口（短暂显示）
        login_window.show()
        
        # 设置定时器自动关闭
        def close_window():
            login_window.close()
            app.quit()
        
        QTimer.singleShot(2000, close_window)  # 2秒后关闭
        
        print("登录窗口测试完成")
        return True
        
    except Exception as e:
        print(f"登录窗口测试失败: {e}")
        return False

def test_main_window():
    """测试主窗口"""
    print("\n测试主窗口...")

    try:
        # 需要先创建 QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        from main_window import MainWindow

        # 创建主窗口（不显示）
        main_window = MainWindow()
        
        # 检查交易所类型
        print(f"交易所类型: {type(main_window.exchange).__name__}")
        print(f"交易所ID: {main_window.exchange.id}")
        
        # 检查环境变量读取
        print(f"API Key 环境变量: {'已设置' if main_window.api_key else '未设置'}")
        print(f"Secret Key 环境变量: {'已设置' if main_window.secret_key else '未设置'}")
        print(f"Passphrase 环境变量: {'已设置' if main_window.passphrase else '未设置'}")
        
        # 检查交易对列表
        symbol_combo = main_window.symbol_combo
        print(f"交易对数量: {symbol_combo.count()}")
        print(f"第一个交易对: {symbol_combo.itemText(0)}")
        
        print("主窗口测试完成")
        return True
        
    except Exception as e:
        print(f"主窗口测试失败: {e}")
        return False

def test_environment_setup():
    """测试环境设置"""
    print("测试环境设置...")
    
    # 检查 .env.example 文件
    if os.path.exists('.env.example'):
        with open('.env.example', 'r') as f:
            content = f.read()
            if 'OKX_API_KEY' in content:
                print("✓ .env.example 已更新为 OKX 格式")
            else:
                print("✗ .env.example 仍使用旧格式")
    else:
        print("✗ .env.example 文件不存在")
    
    # 检查是否存在 .env 文件
    if os.path.exists('.env'):
        print("✓ .env 文件存在")
        from dotenv import load_dotenv
        load_dotenv()
        
        okx_keys = ['OKX_API_KEY', 'OKX_SECRET_KEY', 'OKX_PASSPHRASE']
        for key in okx_keys:
            value = os.getenv(key)
            if value:
                print(f"✓ {key}: 已设置")
            else:
                print(f"✗ {key}: 未设置")
    else:
        print("! .env 文件不存在（这是正常的，用户需要自己创建）")
    
    return True

if __name__ == "__main__":
    print("=" * 60)
    print("OKX 登录器集成测试")
    print("=" * 60)
    
    # 测试环境设置
    test_environment_setup()
    
    # 测试主窗口（不需要GUI）
    test_main_window()
    
    # 测试登录窗口（需要GUI）
    print("\n" + "=" * 60)
    print("启动GUI测试（将显示2秒钟的登录窗口）")
    print("=" * 60)
    test_login_window()
    
    print("\n" + "=" * 60)
    print("集成测试完成")
    print("=" * 60)
