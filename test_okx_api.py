#!/usr/bin/env python3
"""
OKX API 连接测试脚本
用于测试 OKX API 凭证是否有效
"""

import ccxt
import sys

def test_okx_connection():
    """测试 OKX API 连接"""
    print("测试 OKX API 连接...")

    # 测试用的假凭证（用户需要替换为真实凭证）
    api_key = "test_api_key"
    secret_key = "test_secret_key"
    passphrase = "test_passphrase"

    try:
        # 创建 OKX 交易所实例
        exchange = ccxt.okx({
            'apiKey': api_key,
            'secret': secret_key,
            'password': passphrase,
            'enableRateLimit': True,
            'options': {'adjustForTimeDifference': True}
        })

        print("OKX 交易所实例创建成功")
        print(f"交易所名称: {exchange.name}")
        print(f"交易所ID: {exchange.id}")

        # 尝试获取账户余额（这会触发认证）
        print("尝试获取账户余额...")
        balance = exchange.fetch_balance()
        print("API 凭证验证成功！")
        print(f"账户余额: {balance}")

        return True

    except ccxt.AuthenticationError as e:
        print(f"认证错误: {e}")
        print("请检查您的 API Key, Secret Key 和 Passphrase 是否正确")
        return False

    except ccxt.NetworkError as e:
        print(f"网络错误: {e}")
        return False

    except Exception as e:
        print(f"其他错误: {e}")
        return False

def test_ccxt_okx_support():
    """测试 ccxt 是否支持 OKX"""
    print("检查 ccxt 对 OKX 的支持...")

    try:
        # 检查 OKX 是否在支持的交易所列表中
        if 'okx' in ccxt.exchanges:
            print("✓ ccxt 支持 OKX 交易所")

            # 创建一个基本的 OKX 实例（不需要凭证）
            exchange = ccxt.okx()
            print(f"✓ OKX 交易所实例创建成功")
            print(f"  - 名称: {exchange.name}")
            print(f"  - ID: {exchange.id}")
            print(f"  - 版本: {exchange.version}")

            # 测试获取市场数据（不需要认证）
            print("测试获取市场数据...")
            markets = exchange.load_markets()
            print(f"✓ 成功加载 {len(markets)} 个交易对")

            # 显示一些热门交易对
            popular_pairs = ['BTC/USDT', 'ETH/USDT', 'OKB/USDT']
            print("热门交易对:")
            for pair in popular_pairs:
                if pair in markets:
                    market = markets[pair]
                    print(f"  - {pair}: {market['active']}")

            return True
        else:
            print("✗ ccxt 不支持 OKX 交易所")
            print("支持的交易所:", ccxt.exchanges[:10], "...")
            return False

    except Exception as e:
        print(f"测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("OKX API 测试脚本")
    print("=" * 50)

    # 测试 ccxt 对 OKX 的支持
    if test_ccxt_okx_support():
        print("\n" + "=" * 50)
        print("如果您有真实的 OKX API 凭证，请修改脚本中的凭证信息进行测试")
        print("=" * 50)

        # 如果用户提供了真实凭证，可以取消注释下面的行
        # test_okx_connection()
    else:
        print("请确保安装了最新版本的 ccxt 库")
        print("pip install ccxt --upgrade")
        sys.exit(1)
