#!/usr/bin/env python3
"""
OKX 交易对格式测试脚本
用于测试 OKX 交易对的格式和可用性
"""

import ccxt
import sys

def test_okx_symbols():
    """测试 OKX 交易对格式"""
    print("测试 OKX 交易对格式...")

    try:
        # 创建 OKX 交易所实例（不需要凭证）
        exchange = ccxt.okx()

        print("加载市场数据...")
        markets = exchange.load_markets()

        print(f"总共加载了 {len(markets)} 个交易对")

        # 查找一些常见的交易对
        common_symbols = [
            'BTC/USDT', 'BTC/USDT:USDT',  # 现货和永续合约
            'ETH/USDT', 'ETH/USDT:USDT',
            'OKB/USDT', 'OKB/USDT:USDT',
            'SOL/USDT', 'SOL/USDT:USDT',
            'ADA/USDT', 'ADA/USDT:USDT'
        ]

        print("\n检查常见交易对:")
        available_symbols = []
        for symbol in common_symbols:
            if symbol in markets:
                market = markets[symbol]
                print(f"✓ {symbol} - 类型: {market['type']}, 活跃: {market['active']}")
                available_symbols.append(symbol)
            else:
                print(f"✗ {symbol} - 不可用")

        # 显示一些实际可用的交易对
        print(f"\n前20个可用的交易对:")
        count = 0
        for symbol, market in markets.items():
            if market['active'] and count < 20:
                print(f"  {symbol} - 类型: {market['type']}")
                count += 1

        # 测试获取ticker数据
        if available_symbols:
            test_symbol = available_symbols[0]
            print(f"\n测试获取 {test_symbol} 的ticker数据:")
            try:
                ticker = exchange.fetch_ticker(test_symbol)
                print(f"  价格: {ticker['last']}")
                print(f"  24h变化: {ticker['percentage']}%")
                print(f"  成交量: {ticker['baseVolume']}")
            except Exception as e:
                print(f"  获取ticker失败: {e}")

        # 查找永续合约交易对
        print(f"\n永续合约交易对 (USDT结算):")
        swap_count = 0
        for symbol, market in markets.items():
            if market['type'] == 'swap' and 'USDT' in symbol and swap_count < 10:
                print(f"  {symbol}")
                swap_count += 1

        return True

    except Exception as e:
        print(f"测试失败: {e}")
        return False

def test_symbol_conversion():
    """测试交易对格式转换"""
    print("\n" + "="*50)
    print("测试交易对格式转换")
    print("="*50)

    try:
        exchange = ccxt.okx()

        # 测试不同格式的交易对
        test_formats = [
            'BTCUSDT',      # 传统格式
            'BTC-USDT',     # OKX原生格式
            'BTC/USDT',     # CCXT统一格式
            'BTC/USDT:USDT' # 永续合约格式
        ]

        print("测试不同格式的交易对:")
        for format_symbol in test_formats:
            try:
                # 尝试获取市场信息
                if hasattr(exchange, 'market'):
                    market = exchange.market(format_symbol)
                    print(f"✓ {format_symbol} -> {market['symbol']} (类型: {market['type']})")
                else:
                    print(f"? {format_symbol} - 无法直接测试")
            except Exception as e:
                print(f"✗ {format_symbol} - 错误: {e}")

        return True

    except Exception as e:
        print(f"转换测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("OKX 交易对格式测试脚本")
    print("=" * 50)

    if test_okx_symbols():
        test_symbol_conversion()
    else:
        print("基础测试失败，请检查网络连接和ccxt库安装")
        sys.exit(1)
